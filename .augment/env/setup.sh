#!/bin/bash
set -e

echo "🚀 Setting up Next.js 15 Development Environment..."

# Update system packages
sudo apt-get update -y

# Install Node.js 20 (required for Next.js 15)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js version
node_version=$(node --version)
echo "✅ Node.js version: $node_version"

# Install MySQL 8.0
export DEBIAN_FRONTEND=noninteractive
sudo debconf-set-selections <<< 'mysql-server mysql-server/root_password password testpassword123'
sudo debconf-set-selections <<< 'mysql-server mysql-server/root_password_again password testpassword123'
sudo apt-get install -y mysql-server mysql-client

# Start MySQL manually since systemd isn't available
sudo mysqld_safe --user=mysql --datadir=/var/lib/mysql --socket=/var/run/mysqld/mysqld.sock --pid-file=/var/run/mysqld/mysqld.pid &
sleep 10

# Wait for MySQL to be ready
echo "Waiting for MySQL to start..."
for i in {1..30}; do
    if sudo mysql -u root -ptestpassword123 -e "SELECT 1;" &>/dev/null; then
        echo "✅ MySQL is ready"
        break
    fi
    echo "Waiting for MySQL... ($i/30)"
    sleep 2
done

# Create database
sudo mysql -u root -ptestpassword123 -e "CREATE DATABASE IF NOT EXISTS justsimplechat;"
sudo mysql -u root -ptestpassword123 -e "FLUSH PRIVILEGES;"

# Create .env.local file for testing
cat > .env.local << 'EOF'
# Core
NODE_ENV=development
LOG_LEVEL=debug

# URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000
NEXTAUTH_URL=http://localhost:3000

# Database
DATABASE_URL="mysql://root:testpassword123@localhost:3306/justsimplechat"

# Auth
NEXTAUTH_SECRET="test-secret-key-for-development-only"

# OAuth (dummy values for testing)
GOOGLE_CLIENT_ID="test-client-id"
GOOGLE_CLIENT_SECRET="test-client-secret"

# AI Provider Keys (dummy values for basic testing)
OPENAI_API_KEY="sk-test-key"
ANTHROPIC_API_KEY="sk-ant-test-key"
GOOGLE_API_KEY="test-google-key"
OPENROUTER_API_KEY="sk-or-test-key"

# Feature Flags
ENABLE_WEB_SEARCH=false
ENABLE_VOICE_INPUT=false
ENABLE_FILE_UPLOADS=false

# Development
NEXT_PUBLIC_TEST_MODE=true
EOF

# Install dependencies with legacy peer deps (required for React 19)
npm install --legacy-peer-deps

# Install global dependencies
npm install -g tsx prisma typescript

# Generate Prisma client
npx prisma generate

# Push database schema (with retry logic)
echo "Setting up database schema..."
for i in {1..5}; do
    if npx prisma db push --accept-data-loss; then
        echo "✅ Database schema setup successful"
        break
    fi
    echo "Retrying database setup... ($i/5)"
    sleep 3
done

# Build the application
npm run build

# Add executables to PATH
echo 'export PATH="$PATH:./node_modules/.bin"' >> $HOME/.profile
echo 'export PATH="$PATH:/usr/local/bin"' >> $HOME/.profile

echo "✅ Setup completed successfully!"