/**
 * Unified Web Search Handler for AI Providers
 * 
 * This module provides a centralized handler for web search functionality
 * across all AI providers, coordinating between native provider search
 * and fallback Brave Search API
 */

import { prisma } from '@/lib/db';
import { searchManager, BraveSearchProvider } from '@/lib/ai/search';
import { createAISDKProvider } from '@/lib/ai/providers';
import { apiLogger } from '@/lib/logger';
import type { WebSearchResult as SearchResult, StreamingWebSearchChunk } from './providers/types';

export interface WebSearchOptions {
  query: string;
  model: string;
  provider?: string;
  conversationContext?: string;
  limit?: number;
  searchQueries?: string[];
}

export interface WebSearchResponse {
  results: SearchResult[];
  summary: string;
  searchType: 'native' | 'brave' | 'firecrawl';
  searchQueries?: string[];
}

export class WebSearchHandler {
  private aiProvider: any = null;
  private braveSearch: BraveSearchProvider | null = null;

  constructor() {
    // Initialize Brave Search if API key available
    if (process.env.BRAVE_SEARCH_API_KEY) {
      this.braveSearch = new BraveSearchProvider(process.env.BRAVE_SEARCH_API_KEY);
    }
  }

  /**
   * Get or create AI provider for query generation
   */
  private async getAIProvider() {
    if (!this.aiProvider) {
      // Use fast Groq for query generation and summarization
      this.aiProvider = createAISDKProvider('groq');
      await this.aiProvider.validateConfig();
    }
    return this.aiProvider;
  }

  /**
   * Check if a model supports native web search
   */
  async checkNativeSearchSupport(model: string): Promise<{
    supported: boolean;
    config?: any;
  }> {
    try {
      // Query database for model web search support
      const modelRecord = await prisma.models.findFirst({
        where: {
          OR: [
            { canonicalName: model },
            { canonicalName: model.split('/').pop() },
            { displayName: model }
          ]
        },
        select: {
          supportsWebSearch: true,
          canonicalName: true,
          displayName: true
        }
      });

      if (!modelRecord) {
        console.log('[WebSearchHandler] Model not found in database:', model);
        return { supported: false };
      }

      console.log('[WebSearchHandler] Model web search support:', {
        model: modelRecord.canonicalName,
        supported: modelRecord.supportsWebSearch
      });

      return {
        supported: modelRecord.supportsWebSearch === true,
        config: undefined
      };
    } catch (error) {
      console.error('[WebSearchHandler] Error checking model support:', error);
      // Fallback to hardcoded list if database lookup fails
      const NATIVE_SEARCH_MODELS = new Set([
        'perplexity-sonar',
        'sonar-pro',
        'sonar-small',
        'sonar',
        'llama-3.1-sonar-large-128k-online',
        'llama-3.1-sonar-small-128k-online',
        'llama-3.1-sonar-huge-128k-online',
        'grok-2',
        'grok-2-mini',
        'grok-3',
        'grok-3-fast',
        'gpt-4o-search-preview',
        'o1', 'o1-mini', 'o1-preview',
        'o3', 'o3-mini', 'o3-preview', 'o3-search-preview',
        'o4', 'o4-mini', 'o4-preview', 'o4-mini-search-preview',
      ]);
      
      const hasNativeSearch = model && NATIVE_SEARCH_MODELS.has(model.toLowerCase());
      return { supported: !!hasNativeSearch };
    }
  }

  /**
   * Execute web search using appropriate method (for streaming)
   */
  async *executeWebSearch(
    options: WebSearchOptions
  ): AsyncIterable<StreamingWebSearchChunk> {
    const { query, model, provider, conversationContext, limit = 5 } = options;

    // Check if model supports native search
    const { supported, config } = await this.checkNativeSearchSupport(model);

    if (supported) {
      // Model supports native search - return indicator
      yield { type: 'search_start' };
      yield {
        type: 'content',
        content: 'Native web search will be performed by the model.'
      };
      yield { type: 'search_complete' };
      return;
    }

    // Use Brave Search fallback
    console.log('[WebSearchHandler] Using Brave Search fallback for model:', model);
    yield { type: 'search_start' };

    try {
      // Generate optimized search query
      const searchQuery = await this.generateSearchQuery(query, conversationContext);
      
      // Search with Brave
      const results = await this.searchWithBrave(searchQuery, limit);

      // Yield search results
      for (const result of results) {
        yield {
          type: 'search_result',
          searchResult: {
            title: result.title,
            url: result.url,
            snippet: result.snippet
          }
        };
      }

      // Generate summary
      const summary = await this.summarizeResults(results, query);
      yield {
        type: 'content',
        content: summary
      };

      yield { type: 'search_complete' };

    } catch (error) {
      console.error('[WebSearchHandler] Search error:', error);
      throw error;
    }
  }

  /**
   * Execute web search and return results (non-streaming)
   */
  async performWebSearch(options: WebSearchOptions): Promise<WebSearchResponse> {
    const { 
      query, 
      model = 'gpt-4o-mini', 
      provider,
      conversationContext, 
      limit = 5,
      searchQueries 
    } = options;

    console.log('[WebSearchHandler] performWebSearch called:', { query, model, limit });

    // Check if model supports native search
    const { supported, config } = await this.checkNativeSearchSupport(model);

    if (supported) {
      // Return placeholder for native search models
      return {
        results: [],
        summary: 'Web search will be performed natively by the model.',
        searchType: 'native',
        searchQueries: [query]
      };
    }

    // For non-native models, use Brave Search
    console.log('[WebSearchHandler] Using Brave Search for model:', model);
    
    // Use router-provided search query or generate one
    let searchQuery: string;
    if (searchQueries && searchQueries.length > 0) {
      // Use the first router-provided query
      searchQuery = searchQueries[0];
      console.log('[WebSearchHandler] Using router-provided query:', searchQuery);
    } else {
      // Generate a single targeted search query
      searchQuery = await this.generateSearchQuery(query, conversationContext);
      console.log('[WebSearchHandler] Generated search query:', searchQuery);
    }

    // Search with Brave
    console.log('[WebSearchHandler] Calling searchWithBrave...');
    const results = await this.searchWithBrave(searchQuery, limit);
    
    // Fetch additional content from top 3 results for better context
    console.log('[WebSearchHandler] Fetching content from top results...');
    const enrichedResults = await Promise.all(
      results.slice(0, 3).map(async (result, index) => {
        try {
          const content = await this.fetchPageContent(result.url);
          return { 
            ...result, 
            content: content ? content.slice(0, 1000) : undefined // Limit to 1000 chars per page
          };
        } catch (error) {
          console.log(`[WebSearchHandler] Failed to fetch content for result ${index + 1}`);
          return result;
        }
      })
    );
    
    // Combine enriched top results with remaining results
    const finalResults = [
      ...enrichedResults,
      ...results.slice(3)
    ];
    
    // Summarize results with enriched content
    const summary = await this.summarizeResults(finalResults, query);

    return {
      results: finalResults.map(r => ({
        title: r.title,
        url: r.url,
        snippet: r.snippet,
        content: (r as any).content || undefined
      })),
      summary,
      searchType: 'brave',
      searchQueries: [searchQuery]
    };
  }

  /**
   * Generate optimized search query using AI
   */
  async generateSearchQuery(
    userQuery: string,
    conversationContext?: string
  ): Promise<string> {
    console.log('[WebSearchHandler] generateSearchQuery called with:', userQuery);
    try {
      const messages = [{
        role: 'system' as const,
        content: 'You are a search query optimizer. Generate ONE specific, targeted search query that will find the most relevant real-time information.'
      }, {
        role: 'user' as const,
        content: `User's question: "${userQuery}"
${conversationContext ? `\nConversation context: ${conversationContext}` : ''}

Generate a single, specific search query that will find the most relevant and current information.
Focus on key terms and be precise. Return only the search query, nothing else.`
      }];

      console.log('[WebSearchHandler] Calling AI SDK provider...');
      
      // Add timeout wrapper for the API call
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Query generation timeout')), 5000)
      );
      
      // Use AI SDK provider for query generation
      const provider = await this.getAIProvider();
      const response = await Promise.race([
        provider.generateCompletion({
          model: 'llama3-8b-8192', // Fast Groq model
          messages,
          temperature: 0.3, // Lower temperature for more focused queries
          maxTokens: 50,
        }),
        timeoutPromise
      ]);
      
      console.log('[WebSearchHandler] AI SDK response received');

      // AI SDK returns text directly
      const query = (response || '').trim();

      return query.length > 0 ? query : userQuery;
    } catch (error) {
      console.error('[WebSearchHandler] Failed to generate search query:', error);
      apiLogger.error('Failed to generate search query', error);
      // Fallback to original query
      return userQuery;
    }
  }

  /**
   * Search with Brave Search API
   */
  async searchWithBrave(query: string, limit: number = 5): Promise<SearchResult[]> {
    console.log('[WebSearchHandler] searchWithBrave called with query:', query);
    
    if (!this.braveSearch) {
      console.error('[WebSearchHandler] Brave Search API key not configured');
      throw new Error('Brave Search API key not configured');
    }

    try {
      console.log('[WebSearchHandler] Searching for query:', query);
      
      const results = await this.braveSearch.search(query, {
        limit: limit,
        type: 'web',
        safeSearch: true,
      });
      
      console.log(`[WebSearchHandler] Found ${results.length} results for query: ${query}`);
      
      return results;
    } catch (error) {
      console.error('[WebSearchHandler] Error searching with Brave:', error);
      apiLogger.error('Brave search error', error);
      throw error; // Re-throw to handle at higher level
    }
  }

  /**
   * Fetch page content for enrichment
   */
  async fetchPageContent(url: string): Promise<string | null> {
    try {
      console.log('[WebSearchHandler] Attempting to fetch content from:', url);
      
      // Simple fetch with timeout
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), 5000); // 5 second timeout
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; JustSimpleChat/1.0)',
        },
        signal: controller.signal,
      }).finally(() => clearTimeout(timeout));
      
      if (!response.ok) {
        console.log('[WebSearchHandler] Failed to fetch URL:', response.status);
        return null;
      }
      
      const html = await response.text();
      
      // Very basic HTML to text extraction (removes tags)
      const text = html
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
        .replace(/<[^>]+>/g, ' ') // Remove HTML tags
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim()
        .slice(0, 2000); // Limit to 2000 chars
      
      console.log('[WebSearchHandler] Successfully extracted', text.length, 'characters');
      return text;
    } catch (error) {
      console.error('[WebSearchHandler] Error fetching page content:', error);
      return null;
    }
  }

  /**
   * Summarize search results using AI
   */
  async summarizeResults(results: SearchResult[], userQuery: string): Promise<string> {
    console.log('[WebSearchHandler] Starting summarizeResults with', results.length, 'results');
    if (results.length === 0) {
      return 'No search results found.';
    }

    try {
      // Create context from search results
      // Use full content when available, otherwise fall back to snippets
      const context = results.map((r, i) => {
        const contentInfo = (r as any).content 
          ? `Content: ${(r as any).content}` 
          : `Snippet: ${r.snippet}`;
        return `[${i + 1}] ${r.title}\nURL: ${r.url}\n${contentInfo}`;
      }).join('\n\n');

      const messages = [{
        role: 'system' as const,
        content: 'You are a helpful assistant that synthesizes web search results into clear, informative summaries.'
      }, {
        role: 'user' as const,
        content: `Based on the following search results for the query "${userQuery}", provide a comprehensive answer.

Search Results:
${context}

Please:
1. Synthesize the key information from all sources
2. Highlight the most relevant and important points
3. Cite sources using [1], [2], etc. when referencing specific information
4. Keep the summary concise but informative
5. If the results don't fully answer the query, mention what's missing

Summary:`
      }];

      console.log('[WebSearchHandler] Calling summarization through AI SDK...');
      
      // Add timeout wrapper for the API call
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Summarization timeout')), 8000)
      );
      
      // Use AI SDK provider for summarization
      const provider = await this.getAIProvider();
      const response = await Promise.race([
        provider.generateCompletion({
          model: 'llama3-8b-8192', // Fast Groq model
          messages,
          temperature: 0.3,
          maxTokens: 500,
        }),
        timeoutPromise
      ]);

      console.log('[WebSearchHandler] Summary complete');
      return response || '';
    } catch (error) {
      console.error('[WebSearchHandler] Failed to summarize results:', error);
      apiLogger.error('Failed to summarize results', error);
      // Fallback to simple concatenation
      return results.map((r, i) => `[${i + 1}] ${r.title}: ${r.snippet}`).join('\n\n');
    }
  }

  /**
   * Format search results for inclusion in prompts
   */
  formatSearchResults(results: SearchResult[]): string {
    if (results.length === 0) {
      return 'No search results found.';
    }

    const formatted = results
      .slice(0, 5) // Limit to top 5 results
      .map((result, index) => 
        `[${index + 1}] ${result.title}\n   ${result.snippet}\n   Source: ${result.url}`
      )
      .join('\n\n');

    return `Web Search Results:\n\n${formatted}`;
  }
}

// Singleton instance
export const webSearchHandler = new WebSearchHandler();