/**
 * ⚠️  CRITICAL: ONLY USE OFFICIAL OPENAI SDK ⚠️
 * 
 * This file MUST ONLY use the official OpenAI SDK from 'openai' package.
 * NEVER change this to use @ai-sdk/openai or any other wrapper.
 * 
 * Reasons:
 * 1. Direct access to all OpenAI features
 * 2. Support for o-series models with reasoning tokens
 * 3. Native streaming and function calling
 * 4. Official SDK is always up-to-date with latest features
 * 5. Better error handling and debugging
 * 
 * If you need AI SDK compatibility, create a separate adapter.
 */

import OpenAI from 'openai';  // ⚠️ NEVER CHANGE THIS IMPORT ⚠️
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig, NativeWebSearchOptions, StreamingWebSearchChunk } from '../types';

export interface OpenAIProviderConfig extends AISDKProviderConfig {
  apiKey?: string;
  baseURL?: string;
  headers?: Record<string, string>;
}

/**
 * OpenAI Provider using Official OpenAI SDK with AI SDK Compatibility
 * 
 * ⚠️ DO NOT CHANGE TO USE @ai-sdk/openai OR ANY OTHER WRAPPER ⚠️
 * 
 * Implements OpenAI models including GPT-4o, GPT-4o-mini, GPT-3.5-turbo, o-series
 * with support for streaming, function calling, vision, and structured output
 * 
 * Features:
 * - Official OpenAI SDK for maximum compatibility
 * - o-series models with reasoning token tracking
 * - AI SDK compatible wrapper for streaming
 * - Structured outputs with JSON schema
 * - Function calling and tool use
 * - Streaming with proper event handling
 * - Vision and multimodal capabilities
 */
export class OpenAIProvider extends AISdkProviderBase {
  public providerName = 'openai';
  protected provider: any; // AI SDK provider wrapper
  protected client: OpenAI;
  protected config: OpenAIProviderConfig;
  
  constructor(config: OpenAIProviderConfig = {}) {
    super(config);
    this.config = config;
    
    console.log('[OpenAI] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.OPENAI_API_KEY),
      baseUrl: config.baseURL 
    });
    
    // Use the official OpenAI SDK
    this.client = new OpenAI({
      apiKey: config.apiKey || process.env.OPENAI_API_KEY,
      baseURL: config.baseURL,
      defaultHeaders: config.headers,
    });
    
    // Create AI SDK compatible wrapper
    this.provider = this.createProviderWrapper();
    
    console.log('[OpenAI] Created official OpenAI SDK client with AI SDK wrapper');
  }

  // Override the base class generateStream to use our custom implementation
  async *generateStream(options: any): AsyncGenerator<any> {
    console.log('[OpenAI] Using custom generateStream implementation');
    
    // Strip provider prefix from model ID
    const modelId = options.model.replace('openai/', '');
    
    try {
      // Get the model provider wrapper
      const modelProvider = this.provider(modelId);
      
      // Call doStream method
      const result = await modelProvider.doStream({
        messages: options.messages,
        maxTokens: options.maxTokens,
        temperature: options.temperature,
        topP: options.topP,
        presencePenalty: options.presencePenalty,
        frequencyPenalty: options.frequencyPenalty,
        tools: options.tools,
        toolChoice: options.toolChoice,
        user: options.user,
        stop: options.stop
      });
      
      console.log('[OpenAI] Stream created successfully');
      // Yield from the stream
      yield* result.stream;
    } catch (error) {
      console.error('[OpenAI] generateStream failed:', error);
      throw error;
    }
  }

  private createProviderWrapper() {
    // Return a function that creates model instances compatible with AI SDK
    return (modelId: string) => {
      return {
        // For streamText compatibility  
        doStream: async (params: any) => {
          console.log('[OpenAI] Starting stream for model:', modelId);
          
          // Check if this is an o-series model
          const isOSeriesModel = modelId.includes('o1-') || modelId.includes('o3-') || modelId.includes('o4-') ||
                                 modelId === 'o1' || modelId === 'o3' || modelId === 'o4' ||
                                 modelId.startsWith('o1') || modelId.startsWith('o3') || modelId.startsWith('o4');
          
          // Convert AI SDK params to OpenAI params
          const openaiParams: OpenAI.Chat.ChatCompletionCreateParams = {
            model: modelId,
            messages: params.messages,
            stream: true,
            max_tokens: params.maxTokens,
            temperature: isOSeriesModel ? undefined : params.temperature, // o-series don't support temperature
            top_p: isOSeriesModel ? undefined : params.topP,
            presence_penalty: isOSeriesModel ? undefined : params.presencePenalty,
            frequency_penalty: isOSeriesModel ? undefined : params.frequencyPenalty,
          };

          if (isOSeriesModel) {
            throw new Error(`Streaming is not supported for o-series model: ${modelId}`);
          }

          const stream = await this.createStreamingChatCompletion(openaiParams);
          
          // Convert OpenAI stream to AI SDK format
          return {
            stream: (async function* () {
              try {
                for await (const chunk of stream) {
                  const delta = chunk.choices[0]?.delta;
                  if (delta?.content) {
                    yield {
                      type: 'text-delta',
                      textDelta: delta.content,
                    };
                  }
                  
                  // Handle tool calls
                  if (delta?.tool_calls) {
                    for (const toolCall of delta.tool_calls) {
                      if (toolCall.function) {
                        yield {
                          type: 'tool-call-delta',
                          toolCallType: 'function',
                          toolCallId: toolCall.id,
                          toolName: toolCall.function.name,
                          argsTextDelta: toolCall.function.arguments,
                        };
                      }
                    }
                  }

                  // Handle finish reason
                  if (chunk.choices[0]?.finish_reason) {
                    yield {
                      type: 'finish',
                      finishReason: chunk.choices[0].finish_reason === 'stop' ? 'stop' : 
                                   chunk.choices[0].finish_reason === 'length' ? 'length' :
                                   chunk.choices[0].finish_reason === 'tool_calls' ? 'tool-calls' :
                                   chunk.choices[0].finish_reason,
                    };
                  }
                }
              } catch (error) {
                console.error('[OpenAI] Stream error:', error);
                throw error;
              }
            })()
          };
        },

        // For generateText compatibility
        doGenerate: async (params: any) => {
          console.log('[OpenAI] Starting generation for model:', modelId);
          
          const isOSeriesModel = modelId.includes('o1-') || modelId.includes('o3-');
          
          const openaiParams: OpenAI.Chat.ChatCompletionCreateParams = {
            model: modelId,
            messages: params.messages,
            max_tokens: params.maxTokens,
            temperature: isOSeriesModel ? undefined : params.temperature,
            top_p: isOSeriesModel ? undefined : params.topP,
            presence_penalty: isOSeriesModel ? undefined : params.presencePenalty,
            frequency_penalty: isOSeriesModel ? undefined : params.frequencyPenalty,
            tools: params.tools,
            tool_choice: params.toolChoice,
          };

          const completion = await this.createChatCompletion({...openaiParams, stream: false}) as OpenAI.Chat.ChatCompletion;
          
          return {
            text: completion.choices[0]?.message?.content || '',
            toolCalls: completion.choices[0]?.message?.tool_calls?.map(tc => ({
              toolCallType: 'function' as const,
              toolCallId: tc.id,
              toolName: tc.function.name,
              args: JSON.parse(tc.function.arguments),
            })) || [],
            finishReason: completion.choices[0]?.finish_reason === 'stop' ? 'stop' : 
                         completion.choices[0]?.finish_reason === 'length' ? 'length' :
                         completion.choices[0]?.finish_reason === 'tool_calls' ? 'tool-calls' :
                         completion.choices[0]?.finish_reason,
            usage: {
              promptTokens: completion.usage?.prompt_tokens || 0,
              completionTokens: completion.usage?.completion_tokens || 0,
              totalTokens: completion.usage?.total_tokens || 0,
            },
            rawCall: {
              rawPrompt: params.messages,
              rawSettings: openaiParams,
            },
            warnings: [],
            reasoning: isOSeriesModel ? this.extractReasoning(completion) : undefined,
          };
        }
      };
    };
  }

  async validateConfig(): Promise<boolean> {
    try {
      const apiKey = this.config?.apiKey || process.env.OPENAI_API_KEY;
      if (!apiKey) {
        console.error('[OpenAI] No API key found');
        return false;
      }
      
      // Test the configuration with a simple completion
      try {
        const response = await this.client.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [{ role: 'user', content: 'Test' }],
          max_tokens: 5,
        });
        
        console.log('[OpenAI] Configuration validated, provider ready');
        return true;
      } catch (error) {
        console.error('[OpenAI] API validation failed:', error);
        return false;
      }
    } catch (error) {
      console.error('OpenAI configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Create a chat completion using the official OpenAI SDK
   * Supports both regular and o-series models with reasoning
   */
  async createChatCompletion(params: OpenAI.Chat.ChatCompletionCreateParams) {
    // For o-series models, remove unsupported parameters
    const isOSeriesModel = params.model.includes('o1-') || params.model.includes('o3-') || params.model.includes('o4-') ||
                           params.model === 'o1' || params.model === 'o3' || params.model === 'o4' ||
                           params.model.startsWith('o1') || params.model.startsWith('o3') || params.model.startsWith('o4');
    
    if (isOSeriesModel) {
      // o-series models don't support certain parameters
      const { 
        temperature, 
        top_p, 
        frequency_penalty, 
        presence_penalty, 
        stream,
        logprobs,
        top_logprobs,
        ...oSeriesParams 
      } = params;
      
      console.log(`[OpenAI] Creating o-series completion for model: ${params.model}`);
      
      const response = await this.client.chat.completions.create(oSeriesParams as OpenAI.Chat.ChatCompletionCreateParams);
      
      // Log reasoning token usage for o-series models
      if ('usage' in response && response.usage?.completion_tokens_details?.reasoning_tokens) {
        console.log(`[OpenAI] o-series reasoning tokens used: ${response.usage.completion_tokens_details.reasoning_tokens}`);
      }
      
      return response;
    }
    
    return await this.client.chat.completions.create(params);
  }

  /**
   * Create a streaming chat completion (not supported for o-series models)
   */
  async createStreamingChatCompletion(params: OpenAI.Chat.ChatCompletionCreateParams) {
    const isOSeriesModel = params.model.includes('o1-') || params.model.includes('o3-');
    
    if (isOSeriesModel) {
      throw new Error(`Streaming is not supported for o-series model: ${params.model}`);
    }
    
    return await this.client.chat.completions.create({
      ...params,
      stream: true,
    });
  }

  /**
   * Create a streaming chat completion with runner
   * Returns a ChatCompletionStreamingRunner for event handling
   */
  async streamChatCompletion(params: OpenAI.Chat.ChatCompletionCreateParams) {
    const isOSeriesModel = params.model.includes('o1-') || params.model.includes('o3-');
    
    if (isOSeriesModel) {
      throw new Error(`Streaming is not supported for o-series model: ${params.model}`);
    }
    
    return this.client.chat.completions.stream({
      ...params,
      stream: true,
    });
  }

  /**
   * Run tools with automated function calling
   * Note: This requires proper tool function definitions
   */
  async runTools(params: any) {
    return this.client.chat.completions.runTools(params);
  }

  /**
   * Get the underlying OpenAI client for direct access
   */
  getClient(): OpenAI {
    return this.client;
  }

  /**
   * Check if a model is an o-series model
   */
  isOSeriesModel(modelName: string): boolean {
    return modelName.includes('o1-') || modelName.includes('o3-');
  }

  /**
   * Create a structured output completion using response_format
   */
  async createStructuredCompletion(params: OpenAI.Chat.ChatCompletionCreateParams & {
    response_format: { type: 'json_schema'; json_schema: any } | { type: 'json_object' };
  }) {
    return await this.createChatCompletion(params);
  }

  /**
   * Parse a completion response with structured output
   */
  async parseCompletion(params: any) {
    return await this.client.chat.completions.parse(params);
  }

  /**
   * Create a response using the Responses API (for o-series models)
   * This is used for o-series models with reasoning capabilities
   */
  async createResponse(params: {
    model: string;
    input: string;
    modalities?: string[];
    reasoning_effort?: 'low' | 'medium' | 'high';
    stream?: boolean;
  }) {
    // Check if responses API is available
    if (this.client.responses && typeof this.client.responses.create === 'function') {
      return await this.client.responses.create(params);
    } else {
      // Fallback to chat completions for older SDK versions
      return await this.createChatCompletion({
        model: params.model,
        messages: [{ role: 'user', content: params.input }],
        stream: params.stream,
      });
    }
  }

  /**
   * Check if Responses API is available
   */
  hasResponsesAPI(): boolean {
    return !!(this.client.responses && typeof this.client.responses.create === 'function');
  }

  /**
   * Get reasoning tokens from completion usage
   */
  getReasoningTokens(completion: OpenAI.Chat.ChatCompletion): number | null {
    return completion.usage?.completion_tokens_details?.reasoning_tokens || null;
  }

  /**
   * Get available models
   */
  async getModels() {
    return await this.client.models.list();
  }

  /**
   * Extract reasoning content from o-series response
   */
  extractReasoning(completion: OpenAI.Chat.ChatCompletion): string | null {
    // Check if this is an o-series response with reasoning
    const message = completion.choices[0]?.message;
    
    // For newer SDK versions, reasoning might be in a separate field
    if (message && 'reasoning' in message) {
      return (message as any).reasoning;
    }
    
    // For some o-series models, reasoning might be embedded in the content
    if (message?.content && this.isOSeriesModel(completion.model)) {
      // Look for reasoning patterns in the content
      const content = message.content;
      const reasoningMatch = content.match(/^<thinking>(.*?)<\/thinking>/s);
      if (reasoningMatch) {
        return reasoningMatch[1].trim();
      }
    }
    
    return null;
  }

  /**
   * Native web search using OpenAI's web search capabilities
   * Supports gpt-4o-search-preview models and Responses API
   */
  async *streamChatWithWebSearch(
    model: string,
    messages: any[],
    options: {
      webSearch?: NativeWebSearchOptions;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): AsyncIterable<StreamingWebSearchChunk> {
    console.log('[OpenAI] Web search request for model:', model);

    // Check if this is a search-preview model
    const isSearchPreviewModel = model.includes('search-preview');
    const isGpt41Model = model.includes('gpt-4.1');

    yield { type: 'search_start' };

    try {
      if (isSearchPreviewModel) {
        // Use Chat Completions API with web_search_options
        const webSearchOptions: any = {
          search_context_size: 'medium' // low, medium, high
        };

        if (options.webSearch?.userLocation) {
          webSearchOptions.user_location = options.webSearch.userLocation;
        }

        const stream = await this.client.chat.completions.create({
          model: model.replace('openai/', ''),
          messages,
          temperature: options.temperature,
          max_tokens: options.maxTokens,
          stream: options.stream !== false,
          web_search_options: webSearchOptions
        });

        if (!options.stream) {
          // Non-streaming response
          const completion = stream as OpenAI.Chat.ChatCompletion;
          
          // Extract citations from response
          const message = completion.choices[0]?.message;
          if (message?.content) {
            // Parse citations if available
            const citationMatches = message.content.match(/\[(\d+)\]/g);
            if (citationMatches) {
              for (const match of citationMatches) {
                yield {
                  type: 'citation',
                  citation: match
                };
              }
            }

            yield {
              type: 'content',
              content: message.content
            };
          }

          yield { type: 'search_complete' };
          return;
        }

        // Streaming response
        for await (const chunk of stream as AsyncIterable<any>) {
          if (chunk.choices[0]?.delta?.content) {
            yield {
              type: 'content',
              content: chunk.choices[0].delta.content
            };
          }
        }

        yield { type: 'search_complete' };

      } else if (isGpt41Model && this.hasResponsesAPI()) {
        // Use Responses API with web_search_preview tool
        const response = await this.createResponse({
          model: model.replace('openai/', ''),
          input: messages[messages.length - 1].content,
          stream: options.stream !== false
        });

        if (!options.stream) {
          // Non-streaming response
          const result = response as any;
          
          if (result.output) {
            yield {
              type: 'content',
              content: result.output
            };
          }

          yield { type: 'search_complete' };
          return;
        }

        // Streaming response
        for await (const chunk of response as AsyncIterable<any>) {
          if (chunk.delta?.output) {
            yield {
              type: 'content',
              content: chunk.delta.output
            };
          }
        }

        yield { type: 'search_complete' };

      } else {
        throw new Error(`Model ${model} does not support native web search`);
      }

    } catch (error) {
      console.error('[OpenAI] Native web search error:', error);
      throw error;
    }
  }
}

// Export as default for flexibility
export default OpenAIProvider;