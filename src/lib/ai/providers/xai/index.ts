import { xai, createXai } from '@ai-sdk/xai';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig, NativeWebSearchOptions } from '../types';
/**
 * xAI Provider using AI SDK
 * 
 * Implements xAI's Grok models including Grok-3, Grok-3-mini, and Grok-2
 * with support for reasoning content and web search
 * 
 * Models are fetched from database via ModelRepository
 */
export class XAIAISDKProvider extends AISdkProviderBase {
  public providerName = 'xai';
  protected provider: any;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[xAI] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.XAI_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Use createXai when we have custom configuration
    if (config.apiKey || config.baseUrl || config.headers || config.fetch) {
      const customProvider = createXai({
        apiKey: config.apiKey || process.env.XAI_API_KEY,
        baseURL: config.baseUrl,
        headers: config.headers,
        fetch: config.fetch as any
      } as any);
      
      this.provider = customProvider;
      console.log('[xAI] Created custom provider with configuration');
    } else {
      // Use default xai provider instance
      this.provider = xai;
      console.log('[xAI] Using default xAI provider');
    }

    // Override the provider to add Live Search support
    this.provider = this.createLiveSearchEnabledProvider(this.provider);
  }

  /**
   * Create a Live Search enabled provider wrapper
   * Adds xAI's native Live Search capabilities
   */
  private createLiveSearchEnabledProvider(baseProvider: any) {
    return (modelId: string, settings?: any) => {
      const model = baseProvider(modelId, settings);

      // Override the doStream and doGenerate methods to add Live Search support
      const originalDoStream = model.doStream;
      const originalDoGenerate = model.doGenerate;

      model.doStream = async (params: any) => {
        const enhancedParams = this.addLiveSearchParams(params);
        return originalDoStream(enhancedParams);
      };

      model.doGenerate = async (params: any) => {
        const enhancedParams = this.addLiveSearchParams(params);
        return originalDoGenerate(enhancedParams);
      };

      return model;
    };
  }

  /**
   * Add xAI Live Search parameters to the request
   */
  private addLiveSearchParams(params: any) {
    if (!params.webSearchEnabled) {
      return params;
    }

    console.log('[xAI] Enabling Live Search for request');

    // Clone params to avoid mutation
    const enhancedParams = { ...params };

    // Add Live Search parameters
    const webSearchOptions = params.webSearchOptions as NativeWebSearchOptions;

    enhancedParams.search_parameters = {
      mode: 'on', // Force Live Search when web search is enabled
      return_citations: true,
      max_search_results: webSearchOptions?.maxResults || 20,
      sources: []
    };

    // Add web search source
    const webSource: any = {
      type: 'web',
      safe_search: true
    };

    // Add date filtering if specified
    if (webSearchOptions?.dateRange) {
      if (webSearchOptions.dateRange.from || webSearchOptions.dateRange.start) {
        enhancedParams.search_parameters.from_date = webSearchOptions.dateRange.from || webSearchOptions.dateRange.start;
      }
      if (webSearchOptions.dateRange.to || webSearchOptions.dateRange.end) {
        enhancedParams.search_parameters.to_date = webSearchOptions.dateRange.to || webSearchOptions.dateRange.end;
      }
    }

    // Add domain filtering if specified
    if (webSearchOptions?.domains) {
      // Handle both object and array forms
      if (Array.isArray(webSearchOptions.domains)) {
        // Legacy array format - all are included domains
        webSource.allowed_websites = webSearchOptions.domains.slice(0, 5); // Max 5
      } else {
        // New object format with include/exclude
        if (webSearchOptions.domains.include?.length) {
          webSource.allowed_websites = webSearchOptions.domains.include.slice(0, 5); // Max 5
        }
        if (webSearchOptions.domains.exclude?.length) {
          webSource.excluded_websites = webSearchOptions.domains.exclude.slice(0, 5); // Max 5
        }
      }
    }

    enhancedParams.search_parameters.sources.push(webSource);

    // Add X (Twitter) search if enabled
    if (webSearchOptions?.includeTwitter !== false) {
      const xSource: any = {
        type: 'x'
      };

      // Add engagement filters
      if (webSearchOptions?.minEngagement) {
        xSource.post_favorite_count = webSearchOptions.minEngagement;
        xSource.post_view_count = webSearchOptions.minEngagement;
      }

      enhancedParams.search_parameters.sources.push(xSource);
    }

    // Add news search if enabled
    if (webSearchOptions?.includeNews !== false) {
      enhancedParams.search_parameters.sources.push({
        type: 'news',
        safe_search: true
      });
    }

    console.log('[xAI] Live Search parameters:', JSON.stringify(enhancedParams.search_parameters, null, 2));

    return enhancedParams;
  }
  async validateConfig(): Promise<boolean> {
    try {
      // For now, just check if we have an API key
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.XAI_API_KEY;
      if (!apiKey) {
        console.error('[xAI] No API key found');
        return false;
      }
      
      // Skip the actual API call for now to avoid the provider function issue
      console.log('[xAI] Configuration validated (API key present)');
      return true;
    } catch (error) {
      console.error('xAI configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default XAIAISDKProvider;
