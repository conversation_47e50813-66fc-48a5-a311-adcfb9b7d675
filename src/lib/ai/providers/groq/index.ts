import Groq from 'groq-sdk';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig, NativeWebSearchOptions, StreamingWebSearchChunk } from '../types';
import { Stream } from 'groq-sdk/lib/streaming';

/**
 * Groq Provider using Official Groq SDK
 * 
 * Implements Groq's hardware-accelerated models including Llama, Mixtral,
 * and Gemma with ultra-fast inference speeds
 * 
 * Uses the official groq-sdk npm package with streaming via chat.completions.create
 * Models are fetched from database via ModelRepository
 */
export class GroqAISDKProvider extends AISdkProviderBase {
  public providerName = 'groq';
  protected provider: any;
  private groqClient: Groq;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Groq] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.GROQ_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Initialize the official Groq client
    this.groqClient = new Groq({
      apiKey: config.apiKey || process.env.GROQ_API_KEY,
      baseURL: config.baseUrl,
      defaultHeaders: config.headers,
      fetch: config.fetch as any,
    });
    
    // Create a provider wrapper that matches AI SDK interface
    this.provider = this.createProviderWrapper();
    console.log('[Groq] Created official Groq client with provider wrapper');
  }
  
  private createProviderWrapper() {
    // Return a function that creates model instances compatible with AI SDK
    return (modelId: string) => {
      return {
        // For streamText compatibility
        doStream: async (params: any) => {
          try {
            // Convert AI SDK params to Groq params
            const messages = params.messages?.map((msg: any) => ({
              role: msg.role,
              content: msg.content
            }));
            
            // Use the official SDK's streaming API
            const stream = await this.groqClient.chat.completions.create({
              model: modelId,
              messages,
              stream: true,
              temperature: params.temperature,
              max_tokens: params.maxTokens,
              top_p: params.topP,
              frequency_penalty: params.frequencyPenalty,
              presence_penalty: params.presencePenalty,
              stop: params.stop,
              user: params.user,
              seed: params.seed,
            }) as Stream<any>;
            
            // Convert Groq stream to AI SDK compatible stream
            return {
              stream: this.convertStream(stream),
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Groq] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack
            });
            throw error;
          }
        },
        
        // For generateText compatibility
        doGenerate: async (params: any) => {
          try {
            const messages = params.messages?.map((msg: any) => ({
              role: msg.role,
              content: msg.content
            }));
            
            const completion = await this.groqClient.chat.completions.create({
              model: modelId,
              messages,
              stream: false,
              temperature: params.temperature,
              max_tokens: params.maxTokens,
              top_p: params.topP,
              frequency_penalty: params.frequencyPenalty,
              presence_penalty: params.presencePenalty,
              stop: params.stop,
              user: params.user,
              seed: params.seed,
            });
            
            return {
              text: completion.choices[0]?.message?.content || '',
              usage: completion.usage ? {
                promptTokens: completion.usage.prompt_tokens || 0,
                completionTokens: completion.usage.completion_tokens || 0,
                totalTokens: completion.usage.total_tokens || 0
              } : {
                promptTokens: 0,
                completionTokens: 0,
                totalTokens: 0
              },
              finishReason: completion.choices[0]?.finish_reason || 'stop',
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Groq] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack
            });
            throw error;
          }
        }
      };
    };
  }
  
  private async *convertStream(groqStream: Stream<any>): AsyncGenerator<any> {
    try {
      for await (const chunk of groqStream) {
        const delta = chunk.choices[0]?.delta;
        if (delta?.content) {
          yield {
            type: 'text-delta',
            textDelta: delta.content
          };
        }
        
        // Handle finish reason
        if (chunk.choices[0]?.finish_reason) {
          yield {
            type: 'finish',
            finishReason: chunk.choices[0].finish_reason,
            usage: chunk.usage ? {
              promptTokens: chunk.usage.prompt_tokens,
              completionTokens: chunk.usage.completion_tokens,
              totalTokens: chunk.usage.total_tokens
            } : undefined
          };
        }
      }
    } catch (error: any) {
      yield {
        type: 'error',
        error: {
          message: error.message,
          name: error.name,
          stack: error.stack
        }
      };
      throw error;
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.GROQ_API_KEY;
      if (!apiKey) {
        console.error('[Groq] No API key found');
        return false;
      }
      
      // Test the configuration with a minimal API call
      try {
        // Create a minimal completion to test the API
        const completion = await this.groqClient.chat.completions.create({
          model: 'llama3-8b-8192',
          messages: [{ role: 'user', content: 'Hi' }],
          max_tokens: 5,
          temperature: 0
        });
        console.log('[Groq] Configuration validated');
        return true;
      } catch (error: any) {
        console.error('[Groq] API validation failed:', error.message);
        return false;
      }
    } catch (error) {
      console.error('Groq configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Native web search using Groq's agentic tooling
   * Only available on compound-beta and compound-beta-mini models
   */
  async *streamChatWithWebSearch(
    model: string,
    messages: any[],
    options: {
      webSearch?: NativeWebSearchOptions;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): AsyncIterable<StreamingWebSearchChunk> {
    console.log('[Groq] Web search request for model:', model);

    // Verify model supports web search
    if (!model.includes('compound-beta')) {
      throw new Error(`Model ${model} does not support native web search. Use compound-beta or compound-beta-mini`);
    }

    yield { type: 'search_start' };

    try {
      // Build search settings from options
      const searchSettings: any = {};

      if (options.webSearch?.searchDomainFilter) {
        const includeFilters = options.webSearch.searchDomainFilter
          .filter(d => !d.startsWith('-'));
        const excludeFilters = options.webSearch.searchDomainFilter
          .filter(d => d.startsWith('-'))
          .map(d => d.substring(1));
        
        if (includeFilters.length > 0) {
          searchSettings.include_domains = includeFilters;
        }
        if (excludeFilters.length > 0) {
          searchSettings.exclude_domains = excludeFilters;
        }
      }

      // Create the chat completion with search settings
      const params: any = {
        model: model.replace('groq/', ''),
        messages,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
        stream: options.stream !== false,
        search_settings: searchSettings
      };

      if (!options.stream) {
        // Non-streaming response
        const completion = await this.groqClient.chat.completions.create(params);
        
        // Extract search results from response
        const message = completion.choices[0]?.message;
        if (message?.content) {
          // Parse any embedded search results or citations
          // Note: Groq's exact format may vary
          yield {
            type: 'content',
            content: message.content
          };
        }

        yield { type: 'search_complete' };
        return;
      }

      // Streaming response
      const stream = await this.groqClient.chat.completions.create({
        ...params,
        stream: true
      }) as any; // Type assertion needed due to Groq SDK types

      for await (const chunk of stream) {
        if (chunk.choices[0]?.delta?.content) {
          yield {
            type: 'content',
            content: chunk.choices[0].delta.content
          };
        }

        // Check for search results in chunk metadata
        if ((chunk as any).search_results) {
          for (const result of (chunk as any).search_results) {
            yield {
              type: 'search_result',
              searchResult: {
                title: result.title || '',
                url: result.url || '',
                snippet: result.snippet || ''
              }
            };
          }
        }
      }

      yield { type: 'search_complete' };

    } catch (error) {
      console.error('[Groq] Native web search error:', error);
      throw error;
    }
  }
}

// Export as default and named export for flexibility
export default GroqAISDKProvider;