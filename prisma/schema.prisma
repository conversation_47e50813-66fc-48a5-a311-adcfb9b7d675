generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                           String         @id @default(cuid())
  email                        String         @unique
  emailVerified                DateTime?
  name                         String?
  image                        String?
  plan                         User_plan      @default(FREE)
  credits                      Int            @default(10000)
  creditsUsedThisMonth         Int            @default(0)
  billingCycleStart            DateTime       @default(now())
  stripeCustomerId             String?        @unique @map("stripe_customer_id")
  stripeSubscriptionId         String?        @unique @map("stripe_subscription_id")
  stripePriceId                String?        @map("stripe_price_id")
  stripeSubscriptionStatus     String?        @map("stripe_subscription_status")
  stripeCurrentPeriodStart     DateTime?      @map("stripe_current_period_start")
  stripeCurrentPeriodEnd       DateTime?      @map("stripe_current_period_end")
  stripeCancelAtPeriodEnd      Boolean        @default(false) @map("stripe_cancel_at_period_end")
  monthlyMessageLimit          Int            @default(10) @map("monthly_message_limit")
  messagesUsedThisMonth        Int            @default(0) @map("messages_used_this_month")
  premiumMessagesUsedThisMonth Int            @default(0) @map("premium_messages_used_this_month")
  lastResetDate                DateTime       @default(now()) @map("last_reset_date")
  isActive                     Boolean        @default(true) @map("is_active")
  isAdmin                      Boolean?       @default(false) @map("is_admin")
  isBanned                     Boolean        @default(false) @map("is_banned")
  banReason                    String?        @map("ban_reason")
  bannedAt                     DateTime?      @map("banned_at")
  bannedUntil                  DateTime?      @map("banned_until")
  trialEndsAt                  DateTime?      @map("trial_ends_at")
  hasCompletedOnboarding       Boolean        @default(false) @map("has_completed_onboarding")
  onboardingStep               Int            @default(0) @map("onboarding_step")
  preferences                  Json?
  features                     Json?
  theme                        String?        @default("dark")
  language                     String?        @default("en")
  timezone                     String?        @default("UTC")
  referralCode                 String?        @unique @map("referral_code")
  referredBy                   String?        @map("referred_by")
  marketingSource              String?        @map("marketing_source")
  utmSource                    String?        @map("utm_source")
  utmMedium                    String?        @map("utm_medium")
  utmCampaign                  String?        @map("utm_campaign")
  firstName                    String?        @map("first_name")
  lastName                     String?        @map("last_name")
  company                      String?
  jobTitle                     String?        @map("job_title")
  phoneNumber                  String?        @map("phone_number")
  country                      String?
  emailNotifications           Boolean        @default(true) @map("email_notifications")
  marketingEmails              Boolean        @default(false) @map("marketing_emails")
  productUpdates               Boolean        @default(true) @map("product_updates")
  weeklyDigest                 Boolean        @default(false) @map("weekly_digest")
  apiKey                       String?        @unique @map("api_key")
  apiKeyCreatedAt              DateTime?      @map("api_key_created_at")
  apiCallsThisMonth            Int            @default(0) @map("api_calls_this_month")
  apiRateLimit                 Int            @default(1000) @map("api_rate_limit")
  twoFactorEnabled             Boolean        @default(false) @map("two_factor_enabled")
  twoFactorSecret              String?        @map("two_factor_secret")
  lastLoginAt                  DateTime?      @map("last_login_at")
  lastActiveAt                 DateTime?      @map("last_active_at")
  ipAddress                    String?        @map("ip_address")
  userAgent                    String?        @map("user_agent")
  gdprConsent                  Boolean        @default(false) @map("gdpr_consent")
  gdprConsentDate              DateTime?      @map("gdpr_consent_date")
  dataRetentionDays            Int            @default(365) @map("data_retention_days")
  createdAt                    DateTime       @default(now()) @map("created_at")
  updatedAt                    DateTime       @updatedAt @map("updated_at")
  deletedAt                    DateTime?      @map("deleted_at")
  accounts                     Account[]
  conversations                Conversation[]
  messages                     Message[]
  projects                     Project[]
  sessions                     Session[]

  @@index([email])
  @@index([plan])
  @@index([stripeCustomerId])
  @@index([stripeSubscriptionId])
  @@index([stripeSubscriptionStatus])
  @@index([isActive])
  @@index([isBanned])
  @@index([trialEndsAt])
  @@index([referralCode])
  @@index([createdAt])
  @@index([lastActiveAt])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.LongText
  access_token      String? @db.LongText
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.LongText
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expires])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Project {
  id             String         @id @default(cuid())
  name           String
  description    String?
  icon           String?
  color          String?
  userId         String
  organizationId String?
  settings       Json?
  order          Int            @default(0)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  archivedAt     DateTime?
  deletedAt      DateTime?
  conversations  Conversation[]
  user           User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, name])
  @@index([userId])
  @@index([organizationId])
  @@index([archivedAt])
}

model Conversation {
  id            String    @id @default(cuid())
  title         String
  projectId     String?
  userId        String
  model         String?
  provider      String?
  settings      Json?
  metadata      Json?
  publicId      String?   @unique
  sharedWith    Json?
  permissions   Json?
  forkOf        String?
  forkCount     Int       @default(0)
  messageCount  Int       @default(0)
  tokenCount    Int       @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastMessageAt DateTime?
  archivedAt    DateTime?
  deletedAt     DateTime?
  color         String?
  pinned        Boolean   @default(false)
  preview       String?   @db.Text
  tags          Json?
  project       Project?  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages      Message[]

  @@index([projectId])
  @@index([userId])
  @@index([userId, pinned, lastMessageAt(sort: Desc)])
  @@index([userId, archivedAt, lastMessageAt(sort: Desc)])
  @@index([lastMessageAt])
  @@index([publicId])
  @@index([createdAt])
  @@index([pinned])
  @@fulltext([title])
}

model Message {
  id               String       @id @default(cuid())
  conversationId   String
  role             MessageRole
  content          String       @db.LongText
  model            String?
  provider         String?
  modelReasoning   String?      @db.Text
  promptTokens     Int?
  completionTokens Int?
  totalTokens      Int?
  cost             Float?
  ttft             Int?
  totalLatency     Int?
  streamDuration   Int?
  metadata         Json?
  error            Json?
  userId           String?
  parentId         String?
  createdAt        DateTime     @default(now())
  editedAt         DateTime?
  reasoningContent String?      @db.LongText
  attachments      Attachment[]
  conversation     Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user             User?        @relation(fields: [userId], references: [id])
  toolCalls        ToolCall[]

  @@index([conversationId])
  @@index([userId])
  @@index([createdAt])
  @@index([role])
  @@index([parentId])
  @@fulltext([content])
}

model Attachment {
  id            String           @id @default(cuid())
  messageId     String
  type          AttachmentType
  name          String
  size          Int
  mimeType      String
  url           String
  storageKey    String
  status        ProcessingStatus @default(pending)
  processedData Json?
  metadata      Json?
  createdAt     DateTime         @default(now())
  message       Message          @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@index([messageId])
  @@index([type])
}

model ToolCall {
  id          String         @id @default(cuid())
  messageId   String
  tool        String
  input       Json
  output      Json?
  duration    Int?
  status      ToolCallStatus @default(pending)
  error       Json?
  createdAt   DateTime       @default(now())
  completedAt DateTime?
  message     Message        @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@index([messageId])
  @@index([tool])
}

model SearchCache {
  id        String   @id @default(cuid())
  query     String   @db.VarChar(500)
  provider  String
  results   Json
  hitCount  Int      @default(0)
  createdAt DateTime @default(now())
  expiresAt DateTime

  @@unique([query, provider])
  @@index([expiresAt])
}

model UsageLog {
  id             String   @id @default(cuid())
  userId         String
  resource       String
  action         String
  model          String?
  provider       String?
  tokens         Int?
  cost           Float?
  duration       Int?
  conversationId String?
  messageId      String?
  metadata       Json?
  timestamp      DateTime @default(now())

  @@index([userId, timestamp])
  @@index([resource, action])
  @@index([timestamp])
  @@index([conversationId])
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String?
  userEmail  String?
  ipAddress  String?
  userAgent  String?  @db.Text
  action     String
  resource   String?
  resourceId String?
  oldValues  Json?
  newValues  Json?
  sessionId  String?
  requestId  String?
  success    Boolean
  error      Json?
  timestamp  DateTime @default(now())

  @@index([userId])
  @@index([action])
  @@index([timestamp])
  @@index([resource, resourceId])
}

model AnonymousUsage {
  id            String    @id @default(cuid())
  ipAddress     String
  userAgent     String?   @db.Text
  fingerprint   String?
  cookieId      String?
  messagesUsed  Int       @default(0)
  lastMessageAt DateTime?
  resetAt       DateTime
  metadata      Json?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@unique([ipAddress, userAgent(length: 255)])
  @@index([cookieId])
  @@index([fingerprint])
  @@index([resetAt])
  @@index([lastMessageAt])
}

model AILog {
  id                        Int       @id @default(autoincrement())
  requestId                 String    @unique @map("request_id") @db.VarChar(100)
  timestamp                 DateTime  @default(now())
  sessionId                 String?   @map("session_id") @db.VarChar(100)
  requestUrl                String?   @map("request_url") @db.Text
  referer                   String?   @db.Text
  userId                    String?   @map("user_id")
  userEmail                 String?   @map("user_email") @db.VarChar(255)
  userPlan                  String    @map("user_plan") @db.VarChar(50)
  ipAddress                 String    @map("ip_address") @db.VarChar(45)
  userAgent                 String    @map("user_agent") @db.Text
  country                   String?   @db.VarChar(100)
  region                    String?   @db.VarChar(100)
  conversationId            String    @map("conversation_id") @db.VarChar(100)
  messageCount              Int       @map("message_count")
  conversationTitle         String?   @map("conversation_title") @db.VarChar(255)
  isNewConversation         Boolean   @default(false) @map("is_new_conversation")
  wasCondensed              Boolean   @default(false) @map("was_condensed")
  condensationModel         String?   @map("condensation_model") @db.VarChar(100)
  originalTokenCount        Int?      @map("original_token_count")
  condensedTokenCount       Int?      @map("condensed_token_count")
  tokensSaved               Int?      @map("tokens_saved")
  condensationTimeMs        Int?      @map("condensation_time_ms")
  messagesSummarized        Int?      @map("messages_summarized")
  selectedModel             String    @map("selected_model") @db.VarChar(100)
  routerModel               String?   @map("router_model") @db.VarChar(100)
  routerPrompt              String?   @map("router_prompt") @db.Text
  routerResponse            String?   @map("router_response") @db.Text
  routerReasoning           String?   @map("router_reasoning") @db.Text
  routerConfidence          Float?    @map("router_confidence")
  routerAlternatives        Json?     @map("router_alternatives")
  routerTimeMs              Int?      @map("router_time_ms")
  taskType                  String?   @map("task_type") @db.VarChar(50)
  complexity                String?   @db.VarChar(50)
  providerName              String?   @map("provider_name") @db.VarChar(100)
  attemptedProviders        Json?     @map("attempted_providers")
  fallbackCount             Int       @default(0) @map("fallback_count")
  providerErrors            Json?     @map("provider_errors")
  timeToFirstTokenMs        Int?      @map("time_to_first_token_ms")
  totalDurationMs           Int?      @map("total_duration_ms")
  streamingDurationMs       Int?      @map("streaming_duration_ms")
  tokenGenerationRate       Float?    @map("token_generation_rate")
  promptTokens              Int       @map("prompt_tokens")
  completionTokens          Int       @map("completion_tokens")
  totalTokens               Int       @map("total_tokens")
  estimatedCost             Float     @map("estimated_cost")
  requestMessages           Json      @map("request_messages")
  systemPrompt              String?   @map("system_prompt") @db.Text
  finalPrompt               String?   @map("final_prompt") @db.Text
  temperature               Float
  maxTokens                 Int       @map("max_tokens")
  responseContent           String    @map("response_content") @db.LongText
  responseTruncated         Boolean   @map("response_truncated")
  responseError             Json?     @map("response_error")
  status                    String    @db.VarChar(50)
  retryCount                Int       @default(0) @map("retry_count")
  cacheHit                  Boolean   @default(false) @map("cache_hit")
  cacheKey                  String?   @map("cache_key") @db.VarChar(255)
  experimentIds             Json?     @map("experiment_ids")
  featureFlags              Json?     @map("feature_flags")
  userFeedback              String?   @map("user_feedback") @db.VarChar(20)
  regenerated               Boolean   @default(false)
  editedByUser              Boolean   @default(false) @map("edited_by_user")
  webSearchPerformed        Boolean   @default(false) @map("web_search_performed")
  webSearchQuery            String?   @map("web_search_query") @db.Text
  webSearchResults          Int?      @map("web_search_results")
  webSearchTimeMs           Int?      @map("web_search_time_ms")
  rateLimitStatus           String?   @map("rate_limit_status") @db.VarChar(50)
  rateLimitRemaining        Int?      @map("rate_limit_remaining")
  rateLimitReset            DateTime? @map("rate_limit_reset")
  creditsUsed               Int?      @map("credits_used")
  creditsRemaining          Int?      @map("credits_remaining")
  billingTier               String?   @map("billing_tier") @db.VarChar(50)
  systemLoad                Float?    @map("system_load")
  memoryUsageMB             Int?      @map("memory_usage_mb")
  activeConnections         Int?      @map("active_connections")
  queueDepth                Int?      @map("queue_depth")
  browserName               String?   @map("browser_name") @db.VarChar(50)
  browserVersion            String?   @map("browser_version") @db.VarChar(20)
  osName                    String?   @map("os_name") @db.VarChar(50)
  osVersion                 String?   @map("os_version") @db.VarChar(20)
  deviceType                String?   @map("device_type") @db.VarChar(50)
  screenResolution          String?   @map("screen_resolution") @db.VarChar(20)
  viewportSize              String?   @map("viewport_size") @db.VarChar(20)
  timezone                  String?   @db.VarChar(50)
  language                  String?   @db.VarChar(10)
  connectionType            String?   @map("connection_type") @db.VarChar(20)
  effectiveConnectionType   String?   @map("effective_connection_type") @db.VarChar(20)
  downloadSpeedMbps         Float?    @map("download_speed_mbps")
  rttMs                     Int?      @map("rtt_ms")
  cdnNode                   String?   @map("cdn_node") @db.VarChar(50)
  edgeLocation              String?   @map("edge_location") @db.VarChar(50)
  dnsLookupMs               Int?      @map("dns_lookup_ms")
  tcpConnectMs              Int?      @map("tcp_connect_ms")
  tlsHandshakeMs            Int?      @map("tls_handshake_ms")
  requestSendMs             Int?      @map("request_send_ms")
  waitingMs                 Int?      @map("waiting_ms")
  contentDownloadMs         Int?      @map("content_download_ms")
  authCheckMs               Int?      @map("auth_check_ms")
  dbQueryMs                 Int?      @map("db_query_ms")
  modelsConsidered          Json?     @map("models_considered")
  modelScores               Json?     @map("model_scores")
  routingRulesApplied       Json?     @map("routing_rules_applied")
  modelAvailability         Json?     @map("model_availability")
  modelLatencies            Json?     @map("model_latencies")
  modelCosts                Json?     @map("model_costs")
  promptTemplateId          String?   @map("prompt_template_id") @db.VarChar(50)
  promptVersion             String?   @map("prompt_version") @db.VarChar(20)
  promptVariables           Json?     @map("prompt_variables")
  promptOptimizations       Json?     @map("prompt_optimizations")
  contextWindowUsagePercent Float?    @map("context_window_usage_percent")
  specialTokensCount        Int?      @map("special_tokens_count")
  stopTokensUsed            Json?     @map("stop_tokens_used")
  tokenIds                  Json?     @map("token_ids")
  responseLanguage          String?   @map("response_language") @db.VarChar(10)
  responseSentiment         String?   @map("response_sentiment") @db.VarChar(20)
  responseToxicityScore     Float?    @map("response_toxicity_score")
  responseCoherenceScore    Float?    @map("response_coherence_score")
  responseRelevanceScore    Float?    @map("response_relevance_score")
  containsCode              Boolean   @default(false) @map("contains_code")
  codeLanguages             Json?     @map("code_languages")
  containsMath              Boolean   @default(false) @map("contains_math")
  containsTables            Boolean   @default(false) @map("contains_tables")
  containsLists             Boolean   @default(false) @map("contains_lists")
  markdownElements          Json?     @map("markdown_elements")
  errorCategory             String?   @map("error_category") @db.VarChar(50)
  errorSeverity             String?   @map("error_severity") @db.VarChar(20)
  errorStackTrace           String?   @map("error_stack_trace") @db.Text
  recoveryAttempted         Boolean   @default(false) @map("recovery_attempted")
  recoverySuccessful        Boolean   @default(false) @map("recovery_successful")
  fallbackPath              Json?     @map("fallback_path")
  referralSource            String?   @map("referral_source") @db.VarChar(100)
  marketingCampaign         String?   @map("marketing_campaign") @db.VarChar(100)
  utmSource                 String?   @map("utm_source") @db.VarChar(100)
  utmMedium                 String?   @map("utm_medium") @db.VarChar(100)
  utmCampaign               String?   @map("utm_campaign") @db.VarChar(100)
  conversionEvent           String?   @map("conversion_event") @db.VarChar(50)
  revenueImpact             Float?    @map("revenue_impact")
  sessionDurationMs         Int?      @map("session_duration_ms")
  messagesInSession         Int?      @map("messages_in_session")
  userEngagementScore       Float?    @map("user_engagement_score")
  abandonedResponse         Boolean   @default(false) @map("abandoned_response")
  responseReadTimeMs        Int?      @map("response_read_time_ms")
  copiedToClipboard         Boolean   @default(false) @map("copied_to_clipboard")
  sharedResponse            Boolean   @default(false) @map("shared_response")
  followUpMessages          Int?      @map("follow_up_messages")
  serverRegion              String?   @map("server_region") @db.VarChar(50)
  serverInstanceId          String?   @map("server_instance_id") @db.VarChar(100)
  kubernetesPodId           String?   @map("kubernetes_pod_id") @db.VarChar(100)
  containerId               String?   @map("container_id") @db.VarChar(100)
  deploymentVersion         String?   @map("deployment_version") @db.VarChar(50)
  gitCommitHash             String?   @map("git_commit_hash") @db.VarChar(40)
  environment               String?   @db.VarChar(20)
  gdprConsent               Boolean?  @map("gdpr_consent")
  dataClassification        String?   @map("data_classification") @db.VarChar(50)
  containsPii               Boolean   @default(false) @map("contains_pii")
  piiTypes                  Json?     @map("pii_types")
  auditFlags                Json?     @map("audit_flags")
  securityAlerts            Json?     @map("security_alerts")
  embeddingVector           Json?     @map("embedding_vector")
  clusterId                 String?   @map("cluster_id") @db.VarChar(50)
  anomalyScore              Float?    @map("anomaly_score")
  predictedSatisfaction     Float?    @map("predicted_satisfaction")
  predictedChurnRisk        Float?    @map("predicted_churn_risk")
  slackThreadId             String?   @map("slack_thread_id") @db.VarChar(100)
  discordChannelId          String?   @map("discord_channel_id") @db.VarChar(100)
  zendeskTicketId           String?   @map("zendesk_ticket_id") @db.VarChar(100)
  salesforceLeadId          String?   @map("salesforce_lead_id") @db.VarChar(100)
  stripeCustomerId          String?   @map("stripe_customer_id") @db.VarChar(100)
  intercomConversationId    String?   @map("intercom_conversation_id") @db.VarChar(100)
  debugMode                 Boolean   @default(false) @map("debug_mode")
  traceId                   String?   @map("trace_id") @db.VarChar(100)
  spanId                    String?   @map("span_id") @db.VarChar(100)
  parentSpanId              String?   @map("parent_span_id") @db.VarChar(100)
  baggage                   Json?
  debugLogs                 Json?     @map("debug_logs")
  customMetadata            Json?     @map("custom_metadata")
  reservedField1            String?   @map("reserved_field_1") @db.Text
  reservedField2            String?   @map("reserved_field_2") @db.Text
  reservedField3            String?   @map("reserved_field_3") @db.Text
  reservedJson1             Json?     @map("reserved_json_1")
  reservedJson2             Json?     @map("reserved_json_2")
  reservedJson3             Json?     @map("reserved_json_3")

  @@index([anomalyScore])
  @@index([browserName, browserVersion])
  @@index([cacheHit])
  @@index([conversationId])
  @@index([deviceType, osName])
  @@index([environment, deploymentVersion])
  @@index([errorCategory])
  @@index([selectedModel])
  @@index([status])
  @@index([timestamp])
  @@index([userId])
  @@index([utmSource, utmMedium, utmCampaign])
  @@index([wasCondensed])
  @@index([webSearchPerformed])
  @@map("ai_logs")
}

model AIProvider {
  id             String    @id @default(cuid())
  slug           String    @unique
  name           String
  description    String?   @db.VarChar(255)
  detailedInfo   String?   @db.Text
  websiteUrl     String?
  docsUrl        String?
  apiDocsUrl     String?
  statusPageUrl  String?
  logoUrl        String?
  authType       String?   @map("auth_type") @db.VarChar(50)
  authConfig     Json?     @map("auth_config")
  baseUrl        String?   @map("base_url")
  features       Json?
  metadata       Json?
  isActive       Boolean   @default(true)
  isVerified     Boolean   @default(false)
  lastVerifiedAt DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  models         AIModel[]

  @@index([slug])
  @@index([isActive])
  @@fulltext([detailedInfo])
}

model AIModel {
  id                  String                    @id @default(cuid())
  providerId          String
  canonicalName       String
  family              String
  generation          String?                   @db.VarChar(50)
  displayName         String
  description         String?                   @db.VarChar(255)
  detailedInfo        String?                   @db.Text
  modelType           AIModelType
  primaryUseCase      String?
  supportsWebSearch   Boolean                   @default(false)
  webSearchConfig     Json?
  isEnabled           Boolean                   @default(true)
  disabledReason      String?                   @db.VarChar(255)
  disabledAt          DateTime?
  disabledBy          String?
  defaultPriority     Int                       @default(100)
  routerIndex         Int?                      @unique @db.UnsignedInt
  metadata            Json?
  createdAt           DateTime                  @default(now())
  updatedAt           DateTime                  @updatedAt
  validationScore     Decimal?                  @db.Decimal(3, 1)
  validationDate      DateTime?
  validationStatus    AIModel_validationStatus?
  validationNotes     String?                   @db.Text
  provider            AIProvider                @relation(fields: [providerId], references: [id])
  comparisonsAsModel1 ModelComparison[]         @relation("Model1Comparisons")
  comparisonsAsModel2 ModelComparison[]         @relation("Model2Comparisons")
  contextStrategies   ModelContextStrategy[]
  creditCosts         ModelCreditCost[]
  planAccess          ModelPlanAccess[]
  research            ModelResearch[]
  tags                ModelTag[]
  testResults         ModelTestResult[]
  versions            ModelVersion[]

  @@unique([providerId, canonicalName])
  @@index([providerId, family, generation])
  @@index([isEnabled])
  @@index([routerIndex])
  @@fulltext([detailedInfo])
}

model ModelVersion {
  id                String            @id @default(cuid())
  modelId           String
  version           String            @db.VarChar(50)
  versionType       VersionType       @default(DATED)
  releaseDate       DateTime
  deprecatedDate    DateTime?
  sunsetDate        DateTime?
  replacedByVersion String?           @db.VarChar(50)
  changelogUrl      String?
  releaseNotes      String?           @db.Text
  trainingCutoff    DateTime?
  datasetVersion    String?           @db.VarChar(50)
  benchmarkScores   Json?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  endpoints         AIEndpoint[]
  benchmarkResults  BenchmarkResult[]
  model             AIModel           @relation(fields: [modelId], references: [id])

  @@unique([modelId, version])
  @@index([releaseDate])
  @@index([deprecatedDate])
}

model AIEndpoint {
  id                    String                @id @default(cuid())
  versionId             String
  url                   String
  region                String                @db.VarChar(50)
  sku                   String?               @db.VarChar(50)
  sdkVersion            String?               @db.VarChar(50)
  maxContextTokensIn    Int
  maxTokensOut          Int
  optimalContextTokens  Int?
  summarizeAfterTokens  Int?
  supportsStreaming     Boolean               @default(true)
  supportsFunctions     Boolean               @default(false)
  supportsTools         Boolean               @default(false)
  supportsVision        Boolean               @default(false)
  supportsAudio         Boolean               @default(false)
  authMethod            AuthMethod            @default(API_KEY)
  requiresSpecialAccess Boolean               @default(false)
  gdprCompliant         Boolean               @default(false)
  hipaaCompliant        Boolean               @default(false)
  socCompliant          Boolean               @default(false)
  inputCostUSD          Float
  outputCostUSD         Float
  fineTuneCostUSD       Float?
  isEnabled             Boolean               @default(true)
  disabledReason        String?               @db.VarChar(255)
  disabledAt            DateTime?
  disabledBy            String?
  priority              Int                   @default(100)
  routingGroup          String?               @db.VarChar(50)
  isActive              Boolean               @default(true)
  maintenanceMode       Boolean               @default(false)
  isOpenRouter          Boolean               @default(false)
  openRouterModelId     String?
  createdAt             DateTime              @default(now())
  updatedAt             DateTime              @updatedAt
  version               ModelVersion          @relation(fields: [versionId], references: [id])
  alternativeFor        EndpointAlternative[] @relation("AlternativeEndpoint")
  primaryAlternatives   EndpointAlternative[] @relation("PrimaryEndpoint")
  capabilities          EndpointCapability[]
  languages             EndpointLanguage[]
  metrics               EndpointMetric[]
  planLimits            EndpointPlanLimit[]
  rateLimits            EndpointRateLimit[]

  @@unique([versionId, region, sku])
  @@index([url])
  @@index([region])
  @@index([isEnabled, priority])
  @@index([routingGroup, priority])
  @@index([isActive, gdprCompliant])
}

model ModelPlanAccess {
  id               String                   @id @default(cuid())
  modelId          String
  userPlan         ModelPlanAccess_userPlan
  isAvailable      Boolean                  @default(true)
  availableFrom    DateTime?
  availableUntil   DateTime?
  requiresApproval Boolean                  @default(false)
  approvalNotes    String?                  @db.VarChar(255)
  model            AIModel                  @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@unique([modelId, userPlan])
  @@index([userPlan, isAvailable])
}

model EndpointPlanLimit {
  id                String                     @id @default(cuid())
  endpointId        String
  userPlan          EndpointPlanLimit_userPlan
  messagesPerHour   Int?
  messagesPerDay    Int?
  messagesPerMonth  Int?
  tokensPerHour     Int?
  tokensPerDay      Int?
  tokensPerMonth    Int?
  requestsPerMinute Int?
  burstLimit        Int?
  maxContextTokens  Int?
  endpoint          AIEndpoint                 @relation(fields: [endpointId], references: [id], onDelete: Cascade)

  @@unique([endpointId, userPlan])
  @@index([userPlan])
}

model ModelCreditCost {
  id                    String                   @id @default(cuid())
  modelId               String
  userPlan              ModelCreditCost_userPlan
  creditsPerMessage     Float                    @default(1)
  creditsPerToken       Float?
  imageInputMultiplier  Float                    @default(2)
  audioInputMultiplier  Float                    @default(2)
  toolUseMultiplier     Float                    @default(1.5)
  longContextMultiplier Float                    @default(1.5)
  minimumCredits        Float                    @default(1)
  effectiveFrom         DateTime                 @default(now())
  effectiveUntil        DateTime?
  model                 AIModel                  @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@unique([modelId, userPlan, effectiveFrom])
  @@index([userPlan])
  @@index([effectiveFrom, effectiveUntil])
}

model CreditPricing {
  id              String                 @id @default(cuid())
  userPlan        CreditPricing_userPlan
  creditsIncluded Int
  pricePerCredit  Float
  bulkTiers       Json?
  effectiveFrom   DateTime               @default(now())
  effectiveUntil  DateTime?

  @@unique([userPlan, effectiveFrom])
}

model Capability {
  id          String               @id @default(cuid())
  slug        String               @unique
  name        String
  category    String               @db.VarChar(50)
  description String?              @db.VarChar(255)
  endpoints   EndpointCapability[]

  @@index([category])
}

model EndpointCapability {
  id           String     @id @default(cuid())
  endpointId   String
  capabilityId String
  proficiency  Int        @default(5)
  metadata     Json?
  capability   Capability @relation(fields: [capabilityId], references: [id])
  endpoint     AIEndpoint @relation(fields: [endpointId], references: [id], onDelete: Cascade)

  @@unique([endpointId, capabilityId])
  @@index([capabilityId])
}

model Language {
  id         String             @id @default(cuid())
  code       String             @unique @db.VarChar(10)
  name       String             @db.VarChar(50)
  nativeName String?            @db.VarChar(50)
  endpoints  EndpointLanguage[]
}

model EndpointLanguage {
  id            String              @id @default(cuid())
  endpointId    String
  languageId    String
  proficiency   LanguageProficiency
  isSpecialized Boolean             @default(false)
  endpoint      AIEndpoint          @relation(fields: [endpointId], references: [id], onDelete: Cascade)
  language      Language            @relation(fields: [languageId], references: [id])

  @@unique([endpointId, languageId])
  @@index([languageId])
}

model ModelTag {
  id        String  @id @default(cuid())
  modelId   String
  namespace String  @db.VarChar(50)
  key       String  @db.VarChar(50)
  value     String?
  model     AIModel @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@unique([modelId, namespace, key])
  @@index([namespace, key])
  @@index([modelId])
}

model Benchmark {
  id          String            @id @default(cuid())
  slug        String            @unique
  name        String
  category    String            @db.VarChar(50)
  description String?           @db.Text
  metadata    Json?
  results     BenchmarkResult[]

  @@fulltext([description])
}

model BenchmarkResult {
  id             String        @id @default(cuid())
  benchmarkId    String
  versionId      String?
  endpointId     String?
  score          Float
  scoreBreakdown Json?
  percentile     Float?
  testedAt       DateTime
  testConditions Json?
  verified       Boolean       @default(false)
  verifiedBy     String?
  benchmark      Benchmark     @relation(fields: [benchmarkId], references: [id])
  version        ModelVersion? @relation(fields: [versionId], references: [id])

  @@index([benchmarkId, score])
  @@index([versionId])
  @@index([endpointId])
}

model EndpointRateLimit {
  id         String        @id @default(cuid())
  endpointId String
  tier       RateLimitTier
  maxRPM     Int?
  maxTPM     Int?
  maxRPD     Int?
  maxTPD     Int?
  burstLimit Int?
  endpoint   AIEndpoint    @relation(fields: [endpointId], references: [id], onDelete: Cascade)

  @@unique([endpointId, tier])
  @@index([tier])
}

model RouterAnalytics {
  id               String   @id @default(cuid())
  timestamp        DateTime @default(now())
  strategyName     String   @map("strategy_name") @db.VarChar(50)
  taskType         String   @map("task_type") @db.VarChar(50)
  complexity       String   @db.VarChar(20)
  selectedModel    String   @map("selected_model") @db.VarChar(100)
  confidenceScore  Int      @map("confidence_score")
  actualCost       Decimal  @map("actual_cost") @db.Decimal(10, 6)
  ttftMs           Int?     @map("ttft_ms")
  totalDurationMs  Int?     @map("total_duration_ms")
  qualityScore     Decimal? @map("quality_score") @db.Decimal(3, 2)
  userFeedback     Int?     @map("user_feedback")
  userId           String?  @map("user_id")
  conversationId   String?  @map("conversation_id")
  promptTokens     Int?     @map("prompt_tokens")
  completionTokens Int?     @map("completion_tokens")
  errorOccurred    Boolean  @default(false) @map("error_occurred")
  errorMessage     String?  @map("error_message") @db.Text

  @@index([timestamp])
  @@index([strategyName])
  @@index([selectedModel])
  @@index([userId])
  @@map("router_analytics")
}

model ModelPerformance {
  id                 String   @id @default(cuid())
  modelId            String   @map("model_id") @db.VarChar(100)
  taskType           String   @map("task_type") @db.VarChar(50)
  avgQualityScore    Decimal? @map("avg_quality_score") @db.Decimal(3, 2)
  avgCost            Decimal  @map("avg_cost") @db.Decimal(10, 6)
  avgTtftMs          Int?     @map("avg_ttft_ms")
  avgTotalDurationMs Int?     @map("avg_total_duration_ms")
  successRate        Decimal  @map("success_rate") @db.Decimal(3, 2)
  sampleCount        Int      @map("sample_count")
  lastUpdated        DateTime @default(now()) @map("last_updated")

  @@unique([modelId, taskType])
  @@index([modelId])
  @@index([taskType])
}

model StrategyPerformance {
  id                   String   @id @default(cuid())
  strategyName         String   @map("strategy_name") @db.VarChar(50)
  periodStart          DateTime @map("period_start")
  periodEnd            DateTime @map("period_end")
  totalRequests        Int      @map("total_requests")
  successCount         Int      @map("success_count")
  avgConfidence        Decimal  @map("avg_confidence") @db.Decimal(3, 2)
  avgCost              Decimal  @map("avg_cost") @db.Decimal(10, 6)
  avgResponseTimeMs    Int      @map("avg_response_time_ms")
  complexityAccuracy   Decimal? @map("complexity_accuracy") @db.Decimal(3, 2)
  userSatisfactionRate Decimal? @map("user_satisfaction_rate") @db.Decimal(3, 2)

  @@index([strategyName])
  @@index([periodStart, periodEnd])
  @@map("strategy_performance")
}

model RouterExperiment {
  id                    String           @id @default(cuid())
  name                  String           @db.VarChar(100)
  description           String?          @db.Text
  status                ExperimentStatus @default(DRAFT)
  startDate             DateTime?        @map("start_date")
  endDate               DateTime?        @map("end_date")
  controlStrategy       String           @map("control_strategy") @db.VarChar(50)
  treatmentStrategies   Json             @map("treatment_strategies")
  allocationPercentages Json             @map("allocation_percentages")
  successMetrics        Json             @map("success_metrics")
  results               Json?
  createdAt             DateTime         @default(now()) @map("created_at")
  updatedAt             DateTime         @updatedAt @map("updated_at")

  @@index([status])
  @@index([startDate, endDate])
  @@map("router_experiments")
}

model EndpointMetric {
  id             String     @id @default(cuid())
  endpointId     String
  date           DateTime   @db.Date
  latencyP50ms   Int?
  latencyP95ms   Int?
  latencyP99ms   Int?
  avgTTFTms      Int?
  successRate    Float?
  errorRate      Float?
  timeoutRate    Float?
  totalRequests  Int        @default(0)
  totalTokensIn  BigInt     @default(0)
  totalTokensOut BigInt     @default(0)
  costUsdPer1k   Float?
  endpoint       AIEndpoint @relation(fields: [endpointId], references: [id], onDelete: Cascade)

  @@unique([endpointId, date])
  @@index([date])
}

model ContextStrategy {
  id           String                 @id @default(cuid())
  name         String                 @unique @db.VarChar(50)
  description  String?                @db.VarChar(255)
  strategyType ContextStrategyType
  config       Json
  models       ModelContextStrategy[]
}

model ModelContextStrategy {
  id                 String          @id @default(cuid())
  modelId            String
  strategyId         String
  applyAfterTokens   Int
  applyAfterMessages Int?
  configOverrides    Json?
  priority           Int             @default(1)
  model              AIModel         @relation(fields: [modelId], references: [id], onDelete: Cascade)
  strategy           ContextStrategy @relation(fields: [strategyId], references: [id])

  @@unique([modelId, strategyId])
  @@index([modelId, priority])
  @@index([strategyId], map: "ModelContextStrategy_strategyId_fkey")
}

model EndpointRoutingRule {
  id              String   @id @default(cuid())
  name            String
  description     String?  @db.VarChar(255)
  conditions      Json
  preferEndpoints Json
  blockEndpoints  Json
  priority        Int      @default(100)
  isEnabled       Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([isEnabled, priority])
}

model EndpointAlternative {
  id                  String     @id @default(cuid())
  primaryId           String
  alternativeId       String
  priority            Int        @default(1)
  reason              String?    @db.VarChar(255)
  conditions          Json?
  alternativeEndpoint AIEndpoint @relation("AlternativeEndpoint", fields: [alternativeId], references: [id], onDelete: Cascade)
  primaryEndpoint     AIEndpoint @relation("PrimaryEndpoint", fields: [primaryId], references: [id], onDelete: Cascade)

  @@unique([primaryId, alternativeId])
  @@index([primaryId, priority])
  @@index([alternativeId], map: "EndpointAlternative_alternativeId_fkey")
}

model UserModelUsage {
  id           String     @id @default(cuid())
  userId       String
  modelId      String
  endpointId   String
  periodType   PeriodType
  periodStart  DateTime
  messageCount Int        @default(0)
  tokenCount   BigInt     @default(0)
  creditUsed   Float      @default(0)
  lastUsedAt   DateTime   @updatedAt

  @@unique([userId, modelId, periodType, periodStart])
  @@index([userId, periodStart])
  @@index([modelId, periodStart])
}

model ModelResearch {
  id               String               @id @default(cuid())
  modelId          String
  title            String               @db.VarChar(255)
  content          String               @db.Text
  category         ResearchCategory
  tags             Json
  source           String?              @db.VarChar(255)
  sourceUrl        String?              @db.VarChar(500)
  isVerified       Boolean              @default(false)
  verifiedBy       String?
  verifiedAt       DateTime?
  importance       Int                  @default(5)
  isOutdated       Boolean              @default(false)
  supersededBy     String?
  version          Int                  @default(1)
  previousVersion  String?
  relatedModels    Json
  relatedEndpoints Json
  createdBy        String
  createdAt        DateTime             @default(now())
  updatedAt        DateTime             @updatedAt
  model            AIModel              @relation(fields: [modelId], references: [id], onDelete: Cascade)
  attachments      ResearchAttachment[]
  incidents        ResearchIncident[]

  @@index([modelId])
  @@index([category])
  @@index([createdAt])
  @@index([importance])
  @@fulltext([title, content])
}

model ResearchAttachment {
  id          String        @id @default(cuid())
  researchId  String
  fileName    String        @db.VarChar(255)
  fileType    String        @db.VarChar(50)
  fileSize    Int
  fileUrl     String        @db.VarChar(500)
  description String?       @db.VarChar(255)
  uploadedAt  DateTime      @default(now())
  research    ModelResearch @relation(fields: [researchId], references: [id], onDelete: Cascade)

  @@index([researchId])
}

model ResearchIncident {
  id            String           @id @default(cuid())
  researchId    String
  incidentDate  DateTime
  severity      IncidentSeverity
  description   String           @db.Text
  resolution    String?          @db.Text
  affectedUsers Int?
  errorRate     Float?
  createdAt     DateTime         @default(now())
  research      ModelResearch    @relation(fields: [researchId], references: [id], onDelete: Cascade)

  @@index([researchId])
  @@index([incidentDate])
}

model AIModelAudit {
  id          String      @id @default(cuid())
  entityType  String      @db.VarChar(50)
  entityId    String
  action      AuditAction
  changes     Json
  metadata    Json?
  performedBy String?
  reason      String?     @db.VarChar(255)
  createdAt   DateTime    @default(now())

  @@index([entityType, entityId])
  @@index([createdAt])
}

model APICallLog {
  id                       String         @id @default(cuid())
  requestId                String         @unique @map("request_id") @db.VarChar(100)
  timestamp                DateTime       @default(now())
  providerName             String         @map("provider_name") @db.VarChar(100)
  modelId                  String         @map("model_id") @db.VarChar(200)
  endpointUrl              String         @map("endpoint_url") @db.Text
  method                   String         @default("POST") @db.VarChar(20)
  userId                   String?        @map("user_id")
  sessionId                String?        @map("session_id") @db.VarChar(100)
  conversationId           String?        @map("conversation_id") @db.VarChar(100)
  ipAddress                String         @map("ip_address") @db.VarChar(45)
  userAgent                String?        @map("user_agent") @db.Text
  requestHeaders           Json           @map("request_headers")
  requestBodySize          Int            @default(0) @map("request_body_size")
  promptTokens             Int            @default(0) @map("prompt_tokens")
  requestCompressed        Boolean        @default(false) @map("request_compressed")
  statusCode               Int            @map("status_code")
  responseHeaders          Json           @map("response_headers")
  responseBodySize         Int?           @map("response_body_size")
  completionTokens         Int?           @map("completion_tokens")
  totalTokens              Int?           @map("total_tokens")
  responseTimeMs           Int            @map("response_time_ms")
  firstTokenTimeMs         Int?           @map("first_token_time_ms")
  rateLimitLimit           Int?           @map("rate_limit_limit")
  rateLimitRemaining       Int?           @map("rate_limit_remaining")
  rateLimitReset           DateTime?      @map("rate_limit_reset")
  rateLimitRetryAfter      Int?           @map("rate_limit_retry_after")
  rateLimitScope           String?        @map("rate_limit_scope") @db.VarChar(100)
  rateLimitUnit            String?        @map("rate_limit_unit") @db.VarChar(50)
  providerRateLimitHeaders Json?          @map("provider_rate_limit_headers")
  errorType                String?        @map("error_type") @db.VarChar(100)
  errorCode                String?        @map("error_code") @db.VarChar(100)
  errorMessage             String?        @map("error_message") @db.Text
  errorDetails             Json?          @map("error_details")
  isRetryable              Boolean        @default(false) @map("is_retryable")
  retryCount               Int            @default(0) @map("retry_count")
  maxRetriesExceeded       Boolean        @default(false) @map("max_retries_exceeded")
  estimatedCostUsd         Decimal?       @map("estimated_cost_usd") @db.Decimal(10, 8)
  creditsConsumed          Int?           @map("credits_consumed")
  billingTier              String?        @map("billing_tier") @db.VarChar(50)
  dnsLookupMs              Int?           @map("dns_lookup_ms")
  tcpConnectMs             Int?           @map("tcp_connect_ms")
  tlsHandshakeMs           Int?           @map("tls_handshake_ms")
  serverProcessingMs       Int?           @map("server_processing_ms")
  contentTransferMs        Int?           @map("content_transfer_ms")
  totalRequestMs           Int?           @map("total_request_ms")
  serverRegion             String?        @map("server_region") @db.VarChar(50)
  serverInstanceId         String?        @map("server_instance_id") @db.VarChar(100)
  loadBalancerNode         String?        @map("load_balancer_node") @db.VarChar(100)
  cacheHit                 Boolean        @default(false) @map("cache_hit")
  cacheType                String?        @map("cache_type") @db.VarChar(50)
  alertTriggered           Boolean        @default(false) @map("alert_triggered")
  alertType                String?        @map("alert_type") @db.VarChar(100)
  alertSeverity            AlertSeverity? @map("alert_severity")
  requiresAttention        Boolean        @default(false) @map("requires_attention")
  requestType              String         @default("CHAT") @map("request_type") @db.VarChar(50)
  streamingEnabled         Boolean        @default(false) @map("streaming_enabled")
  functionCallingEnabled   Boolean        @default(false) @map("function_calling_enabled")
  visionEnabled            Boolean        @default(false) @map("vision_enabled")
  audioEnabled             Boolean        @default(false) @map("audio_enabled")
  responseQualityScore     Float?         @map("response_quality_score")
  contentSafetyScore       Float?         @map("content_safety_score")
  hallucinationRiskScore   Float?         @map("hallucination_risk_score")
  environment              String         @default("production") @db.VarChar(20)
  deploymentVersion        String?        @map("deployment_version") @db.VarChar(50)
  featureFlags             Json?          @map("feature_flags")
  debugInfo                Json?          @map("debug_info")
  traceId                  String?        @map("trace_id") @db.VarChar(100)
  spanId                   String?        @map("span_id") @db.VarChar(100)
  archivedAt               DateTime?      @map("archived_at")
  retentionDays            Int            @default(90) @map("retention_days")

  @@index([timestamp])
  @@index([providerName])
  @@index([modelId])
  @@index([statusCode])
  @@index([userId])
  @@index([conversationId])
  @@index([rateLimitRemaining])
  @@index([rateLimitReset])
  @@index([errorType])
  @@index([alertTriggered])
  @@index([requiresAttention])
  @@index([responseTimeMs])
  @@index([providerName, statusCode, timestamp])
  @@index([providerName, rateLimitRemaining, timestamp])
  @@index([providerName, errorType, timestamp])
  @@index([providerName, modelId, responseTimeMs, timestamp])
  @@index([userId, estimatedCostUsd, timestamp])
  @@index([archivedAt])
  @@map("api_call_logs")
}

model ModelTestResult {
  id               String   @id @default(cuid())
  modelId          String   @map("model_id")
  testType         String   @map("test_type") @db.VarChar(50)
  success          Boolean  @default(false)
  responseTime     Int?     @map("response_time")
  timeToFirstToken Int?     @map("time_to_first_token")
  totalTokens      Int?     @map("total_tokens")
  error            String?  @db.Text
  metadata         Json?
  createdAt        DateTime @default(now()) @map("created_at")
  model            AIModel  @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@index([modelId, createdAt])
  @@index([testType, createdAt])
  @@index([success, createdAt])
}

model PromptCategory {
  id                  String         @id @default(cuid())
  name                String         @unique @db.VarChar(50)
  displayName         String         @map("display_name") @db.VarChar(100)
  description         String?        @db.Text
  examples            Json?          @map("examples")
  keywords            Json?          @map("keywords")
  complexity          String?        @map("complexity") @db.VarChar(20)
  examplePrompts      Json           @map("example_prompts")
  typicalRequirements Json?          @map("typical_requirements")
  preferredModels     Json?          @map("preferred_models")
  metadata            Json?
  isActive            Boolean        @default(true) @map("is_active")
  createdAt           DateTime       @default(now()) @map("created_at")
  updatedAt           DateTime       @updatedAt @map("updated_at")
  modelMappings       ModelMapping[] @relation("CategoryMappings")

  @@index([isActive])
  @@map("promptCategories")
}

model ModelMapping {
  id                 String         @id @default(cuid())
  category           String         @db.VarChar(50)
  modelId            String         @map("model_id")
  complexityLevel    String         @default("all") @map("complexity_level") @db.VarChar(20)
  score              Int            @default(70)
  usageCount         Int            @default(0) @map("usage_count")
  successCount       Int            @default(0) @map("success_count")
  failureCount       Int            @default(0) @map("failure_count")
  avgUserRating      Decimal?       @map("avg_user_rating") @db.Decimal(3, 2)
  specificAttributes Json?          @map("specific_attributes")
  supportsVision     Boolean        @default(false) @map("supports_vision")
  supportsWebSearch  Boolean        @default(false) @map("supports_web_search")
  enabled            Boolean        @default(true)
  priority           Int            @default(100)
  createdAt          DateTime       @default(now()) @map("created_at")
  updatedAt          DateTime       @updatedAt @map("updated_at")
  categoryRelation   PromptCategory @relation("CategoryMappings", fields: [category], references: [name])

  @@unique([category, modelId, complexityLevel])
  @@index([category])
  @@index([modelId])
  @@index([enabled])
  @@index([complexityLevel])
  @@index([score])
  @@map("modelMappings")
}

model UserFeedback {
  id                        String         @id @default(cuid())
  userId                    String?        @map("user_id")
  sessionId                 String         @map("session_id")
  modelId                   String         @map("model_id")
  category                  String         @db.VarChar(50)
  complexity                String         @db.VarChar(20)
  actionType                FeedbackAction @map("action_type")
  actionDetails             Json?          @map("action_details")
  implicitSatisfactionScore Decimal?       @map("implicit_satisfaction_score") @db.Decimal(3, 2)
  timestamp                 DateTime       @default(now())

  @@index([userId])
  @@index([sessionId])
  @@index([modelId])
  @@index([category])
  @@index([actionType])
  @@index([timestamp])
}

model PromptAnalysisCache {
  id         String   @id @default(cuid())
  promptHash String   @unique @map("prompt_hash") @db.VarChar(64)
  analysis   Json
  hitCount   Int      @default(1) @map("hit_count")
  createdAt  DateTime @default(now()) @map("created_at")
  expiresAt  DateTime @map("expires_at")

  @@index([expiresAt])
}

model RouterSelectionLog {
  id             String   @id @default(cuid())
  requestId      String   @unique @map("request_id")
  selectedModel  String   @map("selected_model")
  reason         String   @db.Text
  category       String   @db.VarChar(50)
  complexity     String   @db.VarChar(20)
  confidence     Decimal  @db.Decimal(3, 2)
  alternatives   Json?
  promptAnalysis Json     @map("prompt_analysis")
  userId         String?  @map("user_id")
  sessionId      String?  @map("session_id")
  timestamp      DateTime @default(now())

  @@index([selectedModel])
  @@index([category])
  @@index([userId])
  @@index([timestamp])
  @@map("router_selection_log")
}

model RouterLLMLog {
  id                      String   @id @default(cuid())
  requestId               String   @map("request_id")
  userId                  String?  @map("user_id")
  sessionId               String?  @map("session_id")
  conversationId          String?  @map("conversation_id")
  inputPrompt             String   @map("input_prompt") @db.Text
  inputConversationLength Int?     @map("input_conversation_length")
  inputHasCode            Boolean  @default(false) @map("input_has_code")
  inputAttachmentTypes    Json?    @map("input_attachment_types")
  inputWebSearchEnabled   Boolean  @default(false) @map("input_web_search_enabled")
  llmModel                String   @default("gemini/gemini-2.5-flash-lite-preview-06-17") @map("llm_model") @db.VarChar(100)
  llmPromptSent           String   @map("llm_prompt_sent") @db.Text
  llmResponse             String   @map("llm_response") @db.Text
  llmTemperature          Decimal? @map("llm_temperature") @db.Decimal(3, 2)
  llmMaxTokens            Int?     @map("llm_max_tokens")
  category                String?  @db.VarChar(50)
  complexity              String?  @db.VarChar(20)
  confidence              Decimal? @db.Decimal(3, 2)
  requirements            Json?
  specificAttributes      Json?    @map("specific_attributes")
  latencyMs               Int?     @map("latency_ms")
  inputTokens             Int?     @map("input_tokens")
  outputTokens            Int?     @map("output_tokens")
  totalTokens             Int?     @map("total_tokens")
  costUsd                 Decimal? @map("cost_usd") @db.Decimal(10, 6)
  success                 Boolean  @default(true)
  errorMessage            String?  @map("error_message") @db.Text
  timestamp               DateTime @default(now())

  @@index([userId])
  @@index([sessionId])
  @@index([conversationId])
  @@index([category])
  @@index([timestamp])
  @@index([requestId])
  @@map("router_llm_logs")
}

model ModelExecutionLog {
  id                  String               @id @default(cuid())
  requestId           String               @map("request_id")
  routerLogId         String?              @map("router_log_id")
  userId              String?              @map("user_id")
  sessionId           String?              @map("session_id")
  conversationId      String?              @map("conversation_id")
  selectedModel       String               @map("selected_model") @db.VarChar(100)
  provider            String?              @db.VarChar(50)
  modelVersion        String?              @map("model_version") @db.VarChar(50)
  fallbackFrom        String?              @map("fallback_from") @db.VarChar(100)
  systemPrompt        String?              @map("system_prompt") @db.Text
  userPrompt          String               @map("user_prompt") @db.Text
  fullPromptSent      String               @map("full_prompt_sent") @db.Text
  attachments         Json?
  modelResponse       String?              @map("model_response") @db.Text
  reasoningContent    String?              @map("reasoning_content") @db.Text
  toolCalls           Json?                @map("tool_calls")
  promptTokens        Int?                 @map("prompt_tokens")
  completionTokens    Int?                 @map("completion_tokens")
  reasoningTokens     Int?                 @map("reasoning_tokens")
  totalTokens         Int?                 @map("total_tokens")
  cachedTokens        Int?                 @map("cached_tokens")
  inputCostUsd        Decimal?             @map("input_cost_usd") @db.Decimal(10, 6)
  outputCostUsd       Decimal?             @map("output_cost_usd") @db.Decimal(10, 6)
  totalCostUsd        Decimal?             @map("total_cost_usd") @db.Decimal(10, 6)
  creditsUsed         Int?                 @map("credits_used")
  timeToFirstTokenMs  Int?                 @map("time_to_first_token_ms")
  totalLatencyMs      Int?                 @map("total_latency_ms")
  streamingDurationMs Int?                 @map("streaming_duration_ms")
  contextWindowUsed   Int?                 @map("context_window_used")
  maxContextWindow    Int?                 @map("max_context_window")
  temperature         Decimal?             @db.Decimal(3, 2)
  maxTokens           Int?                 @map("max_tokens")
  topP                Decimal?             @map("top_p") @db.Decimal(3, 2)
  frequencyPenalty    Decimal?             @map("frequency_penalty") @db.Decimal(3, 2)
  presencePenalty     Decimal?             @map("presence_penalty") @db.Decimal(3, 2)
  stopSequences       Json?                @map("stop_sequences")
  webSearchPerformed  Boolean              @default(false) @map("web_search_performed")
  webSearchQueries    Json?                @map("web_search_queries")
  visionUsed          Boolean              @default(false) @map("vision_used")
  functionCallingUsed Boolean              @default(false) @map("function_calling_used")
  status              ModelExecutionStatus @default(success)
  errorType           String?              @map("error_type") @db.VarChar(100)
  errorMessage        String?              @map("error_message") @db.Text
  retryCount          Int                  @default(0) @map("retry_count")
  userPlan            String?              @map("user_plan") @db.VarChar(20)
  apiVersion          String?              @map("api_version") @db.VarChar(20)
  sdkVersion          String?              @map("sdk_version") @db.VarChar(20)
  clientType          String?              @map("client_type") @db.VarChar(50)
  timestamp           DateTime             @default(now())

  @@index([userId])
  @@index([sessionId])
  @@index([conversationId])
  @@index([selectedModel])
  @@index([timestamp])
  @@index([requestId])
  @@index([routerLogId])
  @@index([status])
}

model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique @db.VarChar(100)
  value       Json
  description String?  @db.Text
  category    String   @default("general") @db.VarChar(50)
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([category])
  @@index([isActive])
  @@map("system_config")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AIModel_backup_20250629_phase2 {
  id              String
  providerId      String
  canonicalName   String
  family          String
  generation      String?                                  @db.VarChar(50)
  displayName     String
  description     String?                                  @db.VarChar(255)
  detailedInfo    String?                                  @db.Text
  modelType       AIModel_backup_20250629_phase2_modelType
  primaryUseCase  String?
  isEnabled       Boolean                                  @default(true)
  disabledReason  String?                                  @db.VarChar(255)
  disabledAt      DateTime?
  disabledBy      String?
  defaultPriority Int                                      @default(100)
  routerIndex     Int?                                     @db.UnsignedInt
  metadata        Json?
  createdAt       DateTime                                 @default(now())
  updatedAt       DateTime

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AIModel_openai_backup_20250629_phase3 {
  id              String
  providerId      String
  canonicalName   String
  family          String
  generation      String?                                         @db.VarChar(50)
  displayName     String
  description     String?                                         @db.VarChar(255)
  detailedInfo    String?                                         @db.Text
  modelType       AIModel_openai_backup_20250629_phase3_modelType
  primaryUseCase  String?
  isEnabled       Boolean                                         @default(true)
  disabledReason  String?                                         @db.VarChar(255)
  disabledAt      DateTime?
  disabledBy      String?
  defaultPriority Int                                             @default(100)
  routerIndex     Int?                                            @db.UnsignedInt
  metadata        Json?
  createdAt       DateTime                                        @default(now())
  updatedAt       DateTime

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ModelPlanAccess_backup_20250629_phase2 {
  id               String
  modelId          String
  userPlan         ModelPlanAccess_backup_20250629_phase2_userPlan
  isAvailable      Boolean                                         @default(true)
  availableFrom    DateTime?
  availableUntil   DateTime?
  requiresApproval Boolean                                         @default(false)
  approvalNotes    String?                                         @db.VarChar(255)

  @@ignore
}

model ValidationAudit {
  id                  Int       @id @default(autoincrement())
  modelId             Int?
  validationBatch     String?   @db.VarChar(255)
  validationStatus    String?   @db.VarChar(50)
  priceSource         String?   @db.VarChar(255)
  oldInputCost        Decimal?  @db.Decimal(10, 6)
  newInputCost        Decimal?  @db.Decimal(10, 6)
  oldOutputCost       Decimal?  @db.Decimal(10, 6)
  newOutputCost       Decimal?  @db.Decimal(10, 6)
  oldPriceScore       Decimal?  @db.Decimal(3, 1)
  newPriceScore       Decimal?  @db.Decimal(3, 1)
  oldPerformanceScore Decimal?  @db.Decimal(3, 1)
  newPerformanceScore Decimal?  @db.Decimal(3, 1)
  oldOverallScore     Decimal?  @db.Decimal(3, 1)
  newOverallScore     Decimal?  @db.Decimal(3, 1)
  notes               String?   @db.Text
  createdAt           DateTime? @default(now()) @db.Timestamp(0)

  @@index([validationBatch], map: "idx_batch")
  @@index([modelId], map: "idx_model")
}

model ConversationLog {
  id               String   @id @default(cuid())
  conversationId   String   @unique @map("conversation_id")
  userId           String?  @map("user_id")
  userEmail        String?  @map("user_email")
  ipAddress        String?  @map("ip_address")
  userAgent        String?  @map("user_agent")
  isAnonymous      Boolean  @default(false) @map("is_anonymous")
  sessionId        String   @map("session_id")
  cookieId         String?  @map("cookie_id")
  title            String?  @db.VarChar(255)
  firstMessage     String   @map("first_message") @db.Text
  messageCount     Int      @default(0) @map("message_count")
  startedAt        DateTime @map("started_at")
  lastActivityAt   DateTime @map("last_activity_at")
  summary          String?  @db.Text
  topicTags        Json?    @map("topic_tags")
  flaggedContent   Boolean  @default(false) @map("flagged_content")
  flagReason       String?  @map("flag_reason") @db.Text
  modelsUsed       Json     @map("models_used")
  totalTokensUsed  Int      @default(0) @map("total_tokens_used")
  totalCostUsd     Decimal  @default(0.000000) @map("total_cost_usd") @db.Decimal(10, 6)
  messages         Json
  routerDecisions  Json?    @map("router_decisions")
  moderationFlags  Json?    @map("moderation_flags")
  blockedResponses Int      @default(0) @map("blocked_responses")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  @@index([userId])
  @@index([sessionId])
  @@index([cookieId])
  @@index([isAnonymous])
  @@index([flaggedContent])
  @@index([startedAt])
  @@map("conversation_logs")
}

model ModelComparison {
  id              String   @id @default(cuid())
  model1Id        String   @map("model1_id")
  model2Id        String   @map("model2_id")
  title           String?  @db.VarChar(255)
  metaDescription String?  @map("meta_description") @db.Text
  content         String?  @db.LongText
  keywords        Json?
  faqItems        Json?    @map("faq_items")
  generatedAt     DateTime @default(now()) @map("generated_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  isStale         Boolean  @default(false) @map("is_stale")
  model1          AIModel  @relation("Model1Comparisons", fields: [model1Id], references: [id], onDelete: Cascade)
  model2          AIModel  @relation("Model2Comparisons", fields: [model2Id], references: [id], onDelete: Cascade)

  @@unique([model1Id, model2Id], name: "unique_comparison")
  @@index([model1Id])
  @@index([model2Id])
  @@index([isStale])
  @@index([updatedAt])
}

model Providers {
  id           String   @id @default(cuid()) @db.VarChar(50)
  name         String   @unique @db.VarChar(100)
  slug         String   @unique @db.VarChar(50)
  package      String   @db.VarChar(100)
  configKeys   Json
  isAiSdk      Boolean  @default(true)
  capabilities Json
  baseUrl      String?  @db.VarChar(255)
  authHeader   String?  @default("Authorization") @db.VarChar(50)
  authPrefix   String?  @default("Bearer") @db.VarChar(20)
  rateLimit    Int?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt
  models       Models[]

  @@map("Providers")
}

model Models {
  id                      String                    @id @default(cuid())
  providerId              String
  canonicalName           String
  displayName             String
  family                  String
  generation              String?                   @db.VarChar(50)
  modelType               AIModelType
  contextWindow           Int                       @default(4096)
  maxOutput               Int                       @default(2048)
  inputCostPer1M          Decimal                   @default(0.000000) @db.Decimal(10, 6)
  outputCostPer1M         Decimal                   @default(0.000000) @db.Decimal(10, 6)
  speedRating             Int                       @default(5) @db.TinyInt
  qualityRating           Int                       @default(5) @db.TinyInt
  supportsVision          Boolean                   @default(false)
  supportsFunctionCalling Boolean                   @default(false)
  supportsWebSearch       Boolean                   @default(false)
  supportsReasoning       Boolean                   @default(false)
  supportsStreaming       Boolean                   @default(true)
  isEnabled               Boolean                   @default(true)
  validationStatus        AIModel_validationStatus?
  disabledReason          String?
  disabledAt              DateTime?
  disabledBy              String?
  extendedMetadata        Json?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  planRules               ModelPlanRules[]
  provider                Providers                 @relation(fields: [providerId], references: [id])

  @@unique([providerId, canonicalName], name: "unique_provider_canonical")
  @@index([providerId, isEnabled], map: "idx_provider_enabled")
  @@index([supportsVision, supportsFunctionCalling], map: "idx_capabilities")
  @@index([speedRating, qualityRating], map: "idx_performance")
  @@index([inputCostPer1M, outputCostPer1M], map: "idx_pricing")
}

model Plans {
  id                  String            @id @default(cuid())
  code                String            @unique @db.VarChar(50)
  displayName         String            @db.VarChar(100)
  description         String?           @db.Text
  tier                Int
  monthlyMessageLimit Int               @default(10)
  monthlyTokenLimit   BigInt?
  dailyMessageLimit   Int?
  hourlyMessageLimit  Int?
  creditsIncluded     Int               @default(0)
  monthlyPriceUSD     Decimal           @default(0.00) @db.Decimal(10, 2)
  yearlyPriceUSD      Decimal           @default(0.00) @db.Decimal(10, 2)
  features            Json?
  metadata            Json?
  isActive            Boolean           @default(true)
  isHidden            Boolean           @default(false)
  availableForSignup  Boolean           @default(true)
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  childInheritances   PlanInheritance[] @relation("ChildPlan")
  parentInheritances  PlanInheritance[] @relation("ParentPlan")

  @@index([tier], map: "idx_tier")
  @@index([isActive, availableForSignup], map: "idx_active")
}

model ModelPlanRules {
  id               String            @id @default(cuid())
  modelId          String
  ruleType         ModelPlanRuleType
  planIds          Json?
  minPlanTier      Int?
  maxPlanTier      Int?
  conditions       Json?
  messagesPerHour  Int?
  messagesPerDay   Int?
  messagesPerMonth Int?
  tokensPerRequest Int?
  priority         Int               @default(100)
  isEnabled        Boolean           @default(true)
  effectiveFrom    DateTime?
  effectiveUntil   DateTime?
  createdAt        DateTime          @default(now())
  model            Models            @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@index([modelId, priority], map: "idx_model_priority")
  @@index([effectiveFrom, effectiveUntil], map: "idx_dates")
}

model PlanInheritance {
  id           String              @id @default(cuid())
  childPlanId  String
  parentPlanId String
  inheritType  PlanInheritanceType @default(FULL)
  childPlan    Plans               @relation("ChildPlan", fields: [childPlanId], references: [id], onDelete: Cascade)
  parentPlan   Plans               @relation("ParentPlan", fields: [parentPlanId], references: [id], onDelete: Cascade)

  @@unique([childPlanId, parentPlanId], name: "unique_inheritance")
  @@index([parentPlanId], map: "PlanInheritance_parentPlanId_fkey")
}

enum ExperimentStatus {
  DRAFT
  RUNNING
  COMPLETED
  CANCELLED
}

enum MessageRole {
  user
  assistant
  system
  function
  tool
}

enum AttachmentType {
  image
  document
  code
  data
  audio
  video
}

enum ProcessingStatus {
  pending
  processing
  completed
  failed
}

enum ToolCallStatus {
  pending
  running
  completed
  failed
}

enum AIModelType {
  TEXT_GENERATION
  CHAT
  COMPLETION
  EMBEDDING
  IMAGE_GENERATION
  AUDIO_GENERATION
  VIDEO_GENERATION
  CODE_GENERATION
  MULTIMODAL
}

enum VersionType {
  SEMVER
  DATED
}

enum AuthMethod {
  API_KEY
  OAUTH
  SIGNED_URL
  CUSTOM
}

enum RateLimitTier {
  FREE
  BASIC
  PRO
  ENTERPRISE
  CUSTOM
}

enum LanguageProficiency {
  BASIC
  CONVERSATIONAL
  FLUENT
  NATIVE
}

enum ContextStrategyType {
  SLIDING_WINDOW
  SUMMARY
  HYBRID
  SEMANTIC
  CUSTOM
}

enum PeriodType {
  HOURLY
  DAILY
  MONTHLY
}

enum ResearchCategory {
  PERFORMANCE_TIPS
  KNOWN_ISSUES
  BEST_PRACTICES
  PROMPT_ENGINEERING
  COST_OPTIMIZATION
  QUALITY_INSIGHTS
  BENCHMARK_NOTES
  API_QUIRKS
  COMPARISON_NOTES
  UPDATE_NOTES
  USE_CASE_EXAMPLES
  SAFETY_CONCERNS
  INTEGRATION_TIPS
  TROUBLESHOOTING
  GENERAL_NOTES
}

enum IncidentSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  ACTIVATE
  DEACTIVATE
  DEPRECATE
  MIGRATE
}

enum AlertSeverity {
  INFO
  WARNING
  ERROR
  CRITICAL
}

enum FeedbackAction {
  completion
  regeneration
  switch
  edit
  copy
  rating
}

enum ModelExecutionStatus {
  success
  error
  timeout
  rate_limited
  cancelled
}

enum CreditPricing_userPlan {
  FREE
  FREEMIUM
  PLUS
  ADVANCED
  MAX
  ENTERPRISE
}

enum EndpointPlanLimit_userPlan {
  FREE
  FREEMIUM
  PLUS
  ADVANCED
  MAX
  ENTERPRISE
}

enum ModelCreditCost_userPlan {
  FREE
  FREEMIUM
  PLUS
  ADVANCED
  MAX
  ENTERPRISE
}

enum ModelPlanAccess_userPlan {
  FREE
  FREEMIUM
  PLUS
  ADVANCED
  MAX
  ENTERPRISE
}

enum ModelPlanAccess_backup_20250629_phase2_userPlan {
  FREE
  FREEMIUM
  PLUS
  ADVANCED
  MAX
}

enum User_plan {
  FREE
  FREEMIUM
  PLUS
  ADVANCED
  MAX
  ENTERPRISE
}

enum AIModel_backup_20250629_phase2_modelType {
  TEXT_GENERATION
  CHAT
  COMPLETION
  EMBEDDING
  IMAGE_GENERATION
  AUDIO_GENERATION
  VIDEO_GENERATION
  CODE_GENERATION
  MULTIMODAL
}

enum AIModel_openai_backup_20250629_phase3_modelType {
  TEXT_GENERATION
  CHAT
  COMPLETION
  EMBEDDING
  IMAGE_GENERATION
  AUDIO_GENERATION
  VIDEO_GENERATION
  CODE_GENERATION
  MULTIMODAL
}

enum AIModel_validationStatus {
  ACTIVE
  DEGRADED
  INACTIVE
}

enum ModelPlanRuleType {
  INCLUDE
  EXCLUDE
  CONDITIONAL
}

enum PlanInheritanceType {
  FULL
  FEATURES_ONLY
  MODELS_ONLY
}
