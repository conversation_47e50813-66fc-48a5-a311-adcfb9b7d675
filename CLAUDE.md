# 🚀 JustSimpleChat Environment Implementation

**Purpose**: Implementation details, environment configurations, and development/deployment procedures for all environments.

## 📖 Quick References
- **Project Overview**: `~/deployments/CLAUDE.md`
- **Personal Preferences**: `~/.claude/CLAUDE.md`
- **📚 Comprehensive Documentation**: `/DOCS/README.md` - Detailed guides for all systems
- **🏛️ System Architecture**: [View Architecture Diagram](./system_diagram.svg) - Complete system overview with all layers and integrations
- **🚀 Database-First Architecture**: Complete provider and model management via MySQL database

## 🌐 Environment Quick Access

### Development Environment (`dev.justsimple.chat:3004`)
- **Branch**: `develop` or feature branches
- **Database**: `justsimplechat_dev`
- **PM2 Process**: `simplechat-dev`
- **Health**: `curl https://dev.justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-dev --nostream`

### Staging Environment (`staging.justsimple.chat:3005`)
- **Branch**: `staging`
- **Database**: `justsimplechat_staging`
- **PM2 Process**: `simplechat-staging`
- **Health**: `curl https://staging.justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-staging --nostream`
- **Deploy**: `cd ~/deployments/dev/simplechat-ai && ./deploy-staging-v3.sh`

### Production Environment (`justsimple.chat:3006`)
- **Branch**: `main` only
- **Database**: `justsimplechat_production`
- **PM2 Process**: `simplechat-production`
- **Health**: `curl https://justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-production --nostream`
- **Deploy**: `cd ~/deployments/dev/simplechat-ai && ./deploy-production-v3.sh`

## 🔧 Shared Environment Configuration

### 🗄️ Database Configuration
- **MySQL Host**: `127.0.0.1` (Docker container)
- **MySQL User**: `root`
- **MySQL Port**: `3306`
- **MySQL Database**: `justsimplechat` (main database, NOT justsimplechat_dev)
- **MySQL Password**: Available in environment variables:
  - `.env` file: `DATABASE_URL` contains full connection string
  - `~/.bashrc`: `MYSQL_PWD` and `MYSQL_PASSWORD` environment variables
  - Direct access: `echo $MYSQL_PWD` or `echo $MYSQL_PASSWORD`

### 🔍 Quick Database Access
```bash
# Connect to MySQL using environment variables
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat

# Or use the full DATABASE_URL from .env
# mysql://root:$MYSQL_PWD@localhost:3306/justsimplechat

# Prisma Schema Location
# /home/<USER>/deployments/dev/simplechat-ai/prisma/schema.prisma
```

### 📊 Redis Configuration
- **Redis Host**: `localhost` (not `127.0.0.1`)
- **Redis Port**: `6379`
- **Redis URL**: `redis://localhost:6379`

### 🔧 Environment Files
- **Primary**: `.env` (contains all configuration)
- **Local overrides**: `.env.local` (if needed)
- **Environment variables**: Available in `~/.bashrc`

## 🔧 MCP Servers Available

### 🌐 Firecrawl (Web Scraping & Crawling)
- **Purpose**: Advanced web scraping, crawling, and content extraction
- **Key Functions**:
  - `firecrawl_scrape`: Single page content extraction (best for known URLs)
  - `firecrawl_map`: Discover all URLs on a website
  - `firecrawl_crawl`: Extract content from multiple pages (async job)
  - `firecrawl_search`: Web search with optional content scraping
  - `firecrawl_extract`: Structured data extraction using LLM
  - `firecrawl_deep_research`: Complex research across multiple sources
  - `firecrawl_generate_llmstxt`: Generate LLMs.txt files for domains
- **Best for**: Website content extraction, competitive analysis, research
- **Usage**: Always check latest docs for parameters and capabilities
- **API Key**: `fc-9fcee76d691b4452b4fbccc283a8e158`
- **Installation**:
  ```bash
  claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
  ```

### 📚 Context7 (Documentation & Library Info)
- **Purpose**: Access up-to-date documentation for libraries and frameworks
- **Key Functions**:
  - `resolve-library-id`: Find Context7-compatible library ID from name
  - `get-library-docs`: Fetch current documentation for libraries
- **Best for**: Getting latest docs for React, Next.js, libraries, frameworks
- **Usage**: Always use `resolve-library-id` first unless user provides exact ID
- **Library Format**: IDs like `/mongodb/docs`, `/vercel/next.js`, `/supabase/supabase`
- **Installation** (no API key needed):
  ```bash
  claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest
  ```

### 🔍 Perplexity (AI Search & Research)
- **Purpose**: AI-powered search and research capabilities
- **Key Functions**:
  - `search`: Quick search for simple queries (Sonar Pro model)
  - `reason`: Complex, multi-step reasoning tasks (Sonar Reasoning Pro)
  - `deep_research`: In-depth analysis with detailed reports (Sonar Deep Research)
- **Best for**: Current events, latest information, research questions
- **Usage**: Supports conversation context and can access real-time web data
- **API Key**: `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`
- **Installation**:
  ```bash
  claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
  ```

### 🚨 Important MCP Usage Rules
1. **Always use MCP tools for latest documentation** - Don't rely on outdated knowledge
2. **Check capabilities before assuming** - Each tool has specific use cases
3. **Firecrawl for web content** - Use appropriate function for your needs
4. **Context7 for library docs** - Essential for React 19, Next.js 15, etc.
5. **Perplexity for research** - When you need current, accurate information
6. **Verify before implementing** - Always check latest docs before coding

### 🎯 Common MCP Use Cases
- **Library Implementation**: Use Context7 to get latest API docs
- **Web Research**: Use Firecrawl for content extraction or Perplexity for AI search
- **Competitive Analysis**: Use Firecrawl to analyze competitor sites
- **Documentation**: Use Context7 for framework-specific guides
- **Current Events**: Use Perplexity for real-time information

### 🚀 MCP Server Management

#### Quick Installation (All Three Servers)
```bash
# Set timeout first to prevent installation timeouts
export BASH_DEFAULT_TIMEOUT_MS=600000

# Install all MCP servers
claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest

# Verify installation
claude mcp list
```

#### Management Commands
```bash
# List all installed MCP servers
claude mcp list

# Get details about a specific server
claude mcp get perplexity-ask
claude mcp get firecrawl
claude mcp get context7

# Remove a server (if needed)
claude mcp remove perplexity-ask --scope user
claude mcp remove firecrawl --scope user
claude mcp remove context7 --scope user
```

#### Checking MCP Availability in Claude
Use the `/mcp` command in any Claude session to see available MCP tools. If tools are missing, reinstall using the commands above.

### 📊 Current MCP Server Status (July 2025)

#### ✅ Working MCP Servers
1. **Perplexity** - AI-powered search (3 models: search, reason, deep_research)
2. **Firecrawl** - Web scraping and crawling
3. **Context7** - Library documentation
4. **Everything** - Multiple tools from @modelcontextprotocol

#### ❌ Not Yet Available
Direct AI model MCP servers for:
- OpenAI (GPT-4, o3, etc.)
- Gemini/Google
- Anthropic Claude
- Groq, XAI, Mistral

**Note**: These attempted installations were removed as the npm packages don't exist yet. For AI model access, use API keys directly in your application.

#### 🔧 Available API Keys
**See**: `~/deployments/CLAUDE.md` line 309 for complete environment variable reference

## 🎯 Model Configuration Notes

### Database-First Model Management (Updated July 10, 2025)

#### 🚨 CRITICAL DATABASE STRUCTURE (DO NOT CHANGE)
- **Database canonicalName INCLUDES provider prefix**: `openai/gpt-4o`, `xai/grok-3`, `gemini/gemini-1.5-pro`
- **This canonicalName is used directly by AI SDK** - no transformation needed!
- **ALL 209 database models now have consistent prefixes** (updated July 10, 2025)

#### Current Status (July 10, 2025 - DATABASE-FIRST ARCHITECTURE COMPLETE!)
- **Database Models**: 209 total (all with provider prefixes)
- **Providers Table**: 21 providers with complete configuration
  - ✅ All 209 models linked to valid providers with foreign key constraints
  - ✅ ProviderRepository with Redis caching (5-minute TTL)
  - ✅ Direct provider SDK integration (no proxy layer)
- **Working Providers**: 21/21 ✅ **ALL PROVIDERS OPERATIONAL!**
  - ✅ OpenAI (direct SDK integration)
  - ✅ Anthropic (direct SDK with x-api-key authentication)
  - ✅ Google/Gemini (direct Google AI SDK)
  - ✅ Perplexity (direct SDK integration)
  - ✅ Groq (direct SDK integration)
  - ✅ xAI (direct SDK integration)
  - ✅ DeepSeek (direct SDK integration)
  - ✅ Mistral (direct SDK integration)
  - ✅ Cohere (direct SDK integration)
  - ✅ OpenRouter (direct API integration)
  - ✅ Together AI (direct SDK integration)
  - ✅ Plus 10 additional providers in Providers table

#### What We Accomplished Today (July 10)
1. **Providers Table**: Created complete schema with 21 providers and full configuration
2. **Foreign Key Constraints**: All 209 models properly linked to valid providers
3. **Repository Pattern**: Implemented ProviderRepository with Redis caching (5-minute TTL)
4. **Direct SDK Integration**: All providers use their specific SDKs (Google SDK, Anthropic SDK, etc.)
5. **Architecture Migration**: **Database-first architecture** with direct provider SDK integration
6. **Database-First Success**: Achieved 100% database-first architecture (21/21 providers)

#### Critical Database Architecture Rules
- **Database Format = AI SDK Format**: Both use `provider/model` (e.g., `openai/gpt-4o`)
- **NO transformation needed**: Database canonicalName maps directly to provider SDKs
- **Consistent prefixes REQUIRED**: 
  - OpenAI: `openai/` (e.g., `openai/gpt-4o`, `openai/dall-e-3`)
  - Anthropic: `anthropic/`
  - Google: `gemini/` (for Google AI SDK)
  - xAI: `xai/` (e.g., `xai/grok-3`, `xai/grok-3-mini`, `xai/grok-2-1212`)
  - Together AI: `together_ai/`
  - OpenRouter: `openrouter/`
  - Alibaba: `alibaba/`
  - All other providers: `provider/`

#### Database-First Provider Configuration
```
Providers Table (21 providers):
openai        ✅ Direct OpenAI SDK integration
anthropic     ✅ Direct Anthropic SDK (x-api-key auth)
google        ✅ Direct Google AI SDK
perplexity    ✅ Direct Perplexity SDK
groq          ✅ Direct Groq SDK
deepseek      ✅ Direct DeepSeek SDK
mistral       ✅ Direct Mistral SDK
cohere        ✅ Direct Cohere SDK
alibaba       ✅ Direct Alibaba API
openrouter    ✅ Direct OpenRouter API
xai           ✅ Direct xAI SDK
together      ✅ Direct Together AI SDK
+ 9 more providers in Providers table
```

### Provider-Specific Configuration

#### xAI Models (FIXED July 2025)
- **Issue**: Authentication was previously not working correctly
- **Solution**: Re-added all xAI models with proper configuration
- **Status**: All 8 xAI models working (grok-3, grok-3-mini, grok-2-1212 series)
- **API Key**: Stored in .env as `XAI_API_KEY` (full key starting with `xai-`)
- **Note**: All authentication handled via direct provider SDKs

#### Together AI Models (FIXED July 2025)
- **Issue**: Credential reference system not working
- **Solution**: Re-added models with proper configuration
- **Status**: All 45 Together AI models working
- **API Key**: Stored as `TOGETHER_API_KEY` in .env
- **Models**: 45 models including Llama, QwQ, Mixtral series
#### Alibaba/Qwen Models Configuration
- Alibaba/Qwen models use `alibaba/` prefix and are accessed via OpenAI-compatible API

## 🚀 Deployment Process

For complete deployment instructions using the v3.2 scripts, see:
**`~/deployments/CLAUDE.md`** - Section "🚀 Deployment Process"

### Quick Reference
- **Staging**: `./deploy-staging-v3.sh` (from develop branch)
- **Production**: `./deploy-production-v3.sh` (from main branch)
- **Timeout**: Always set `export BASH_DEFAULT_TIMEOUT_MS=1200000` first

### Development-Specific Notes
- Model statistics are now generated at runtime (no build-time generation)
- Each environment has separate `.env` and `.env.local` files
- Redis must use `localhost` not `127.0.0.1` in URLs
- All deployment logs saved to `/home/<USER>/deployments/logs/deploy-*.log`

---
**Environment Implementation**: All environments, configuration, and deployment procedures
**Last Updated**: July 6, 2025