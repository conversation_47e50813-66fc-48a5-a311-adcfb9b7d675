# 🚀 JustSimpleChat Environment Implementation

**Purpose**: Implementation details, environment configurations, and development/deployment procedures for all environments.

## 📖 Quick References
- **Project Overview**: `~/deployments/CLAUDE.md`
- **Personal Preferences**: `~/.claude/CLAUDE.md`
- **📚 Comprehensive Documentation**: `/DOCS/README.md` - Detailed guides for all systems
- **🏛️ System Architecture**: [View Architecture Diagram](./system_diagram.svg) - Complete system overview with all layers and integrations
- **🚀 Database-First Architecture**: Complete provider and model management via MySQL database

## 🌐 Environment Quick Access

### Development Environment (`dev.justsimple.chat:3004`)
- **Branch**: `develop` or feature branches
- **Database**: `justsimplechat_dev`
- **PM2 Process**: `simplechat-dev`
- **Health**: `curl https://dev.justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-dev --nostream`

### Staging Environment (`staging.justsimple.chat:3005`)
- **Branch**: `staging`
- **Database**: `justsimplechat_staging`
- **PM2 Process**: `simplechat-staging`
- **Health**: `curl https://staging.justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-staging --nostream`
- **Deploy**: `cd ~/deployments/dev/simplechat-ai && ./deploy-staging-v3.sh`

### Production Environment (`justsimple.chat:3006`)
- **Branch**: `main` only
- **Database**: `justsimplechat_production`
- **PM2 Process**: `simplechat-production`
- **Health**: `curl https://justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-production --nostream`
- **Deploy**: `cd ~/deployments/dev/simplechat-ai && ./deploy-production-v3.sh`

## 🔧 Shared Environment Configuration

### 🗄️ Database Configuration
- **MySQL Host**: `127.0.0.1` (Docker container)
- **MySQL User**: `root`
- **MySQL Port**: `3306`
- **MySQL Database**: `justsimplechat_production`
- **MySQL Password**: Available in environment variables:
  - `.env` file: `DATABASE_URL` contains full connection string
  - `~/.bashrc`: `MYSQL_PWD` and `MYSQL_PASSWORD` environment variables
  - Direct access: `echo $MYSQL_PWD` or `echo $MYSQL_PASSWORD`

### 🔍 Quick Database Access
```bash
# Connect to MySQL using environment variables
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production

# Or use the full DATABASE_URL from .env
# mysql://root:Dmggg13031988***@localhost:3306/justsimplechat_production
```

### 📊 Redis Configuration
- **Redis Host**: `localhost` (not `127.0.0.1`)
- **Redis Port**: `6379`
- **Redis URL**: `redis://localhost:6379`

### 🔧 Environment Files
- **Primary**: `.env` (contains all configuration)
- **Local overrides**: `.env.local` (if needed)
- **Environment variables**: Available in `~/.bashrc`

## 🔧 MCP Servers Available

### 🌐 Firecrawl (Web Scraping & Crawling)
- **Purpose**: Advanced web scraping, crawling, and content extraction
- **Key Functions**:
  - `firecrawl_scrape`: Single page content extraction (best for known URLs)
  - `firecrawl_map`: Discover all URLs on a website
  - `firecrawl_crawl`: Extract content from multiple pages (async job)
  - `firecrawl_search`: Web search with optional content scraping
  - `firecrawl_extract`: Structured data extraction using LLM
  - `firecrawl_deep_research`: Complex research across multiple sources
  - `firecrawl_generate_llmstxt`: Generate LLMs.txt files for domains
- **Best for**: Website content extraction, competitive analysis, research
- **Usage**: Always check latest docs for parameters and capabilities
- **API Key**: `fc-9fcee76d691b4452b4fbccc283a8e158`
- **Installation**:
  ```bash
  claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
  ```

### 📚 Context7 (Documentation & Library Info)
- **Purpose**: Access up-to-date documentation for libraries and frameworks
- **Key Functions**:
  - `resolve-library-id`: Find Context7-compatible library ID from name
  - `get-library-docs`: Fetch current documentation for libraries
- **Best for**: Getting latest docs for React, Next.js, libraries, frameworks
- **Usage**: Always use `resolve-library-id` first unless user provides exact ID
- **Library Format**: IDs like `/mongodb/docs`, `/vercel/next.js`, `/supabase/supabase`
- **Installation** (no API key needed):
  ```bash
  claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest
  ```

### 🔍 Perplexity (AI Search & Research)
- **Purpose**: AI-powered search and research capabilities
- **Key Functions**:
  - `search`: Quick search for simple queries (Sonar Pro model)
  - `reason`: Complex, multi-step reasoning tasks (Sonar Reasoning Pro)
  - `deep_research`: In-depth analysis with detailed reports (Sonar Deep Research)
- **Best for**: Current events, latest information, research questions
- **Usage**: Supports conversation context and can access real-time web data
- **API Key**: `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`
- **Installation**:
  ```bash
  claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
  ```

### 🚨 Important MCP Usage Rules
1. **Always use MCP tools for latest documentation** - Don't rely on outdated knowledge
2. **Check capabilities before assuming** - Each tool has specific use cases
3. **Firecrawl for web content** - Use appropriate function for your needs
4. **Context7 for library docs** - Essential for React 19, Next.js 15, etc.
5. **Perplexity for research** - When you need current, accurate information
6. **Verify before implementing** - Always check latest docs before coding

### 🎯 Common MCP Use Cases
- **Library Implementation**: Use Context7 to get latest API docs
- **Web Research**: Use Firecrawl for content extraction or Perplexity for AI search
- **Competitive Analysis**: Use Firecrawl to analyze competitor sites
- **Documentation**: Use Context7 for framework-specific guides
- **Current Events**: Use Perplexity for real-time information

### 🚀 MCP Server Management

#### Quick Installation (All Three Servers)
```bash
# Set timeout first to prevent installation timeouts
export BASH_DEFAULT_TIMEOUT_MS=600000

# Install all MCP servers
claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest

# Verify installation
claude mcp list
```

#### Management Commands
```bash
# List all installed MCP servers
claude mcp list

# Get details about a specific server
claude mcp get perplexity-ask
claude mcp get firecrawl
claude mcp get context7

# Remove a server (if needed)
claude mcp remove perplexity-ask --scope user
claude mcp remove firecrawl --scope user
claude mcp remove context7 --scope user
```

#### Checking MCP Availability in Claude
Use the `/mcp` command in any Claude session to see available MCP tools. If tools are missing, reinstall using the commands above.

### 📊 Current MCP Server Status (July 2025)

#### ✅ Working MCP Servers
1. **Perplexity** - AI-powered search (3 models: search, reason, deep_research)
2. **Firecrawl** - Web scraping and crawling
3. **Context7** - Library documentation
4. **Everything** - Multiple tools from @modelcontextprotocol

#### ❌ Not Yet Available
Direct AI model MCP servers for:
- OpenAI (GPT-4, o3, etc.)
- Gemini/Google
- Anthropic Claude
- Groq, XAI, Mistral

**Note**: These attempted installations were removed as the npm packages don't exist yet. For AI model access, use API keys directly in your application.

#### 🔧 Available API Keys in .env
- `OPENAI_API_KEY` - GPT models
- `ANTHROPIC_API_KEY` - Claude models
- `GOOGLE_API_KEY` - Gemini models
- `GROQ_API_KEY` - Groq fast inference
- `XAI_API_KEY` - Grok models
- `MISTRAL_API_KEY` - Mistral models
- `DEEPSEEK_API_KEY` - DeepSeek models
- `COHERE_API_KEY` - Cohere models
- `TOGETHER_API_KEY` - Together AI models

## 🎯 Model Configuration Notes

### Database-First Model Management (Updated July 10, 2025)

#### 🚨 CRITICAL DATABASE STRUCTURE (DO NOT CHANGE)
- **Database canonicalName INCLUDES provider prefix**: `openai/gpt-4o`, `xai/grok-3`, `gemini/gemini-1.5-pro`
- **This canonicalName is used directly by AI SDK** - no transformation needed!
- **ALL 209 database models now have consistent prefixes** (updated July 10, 2025)

#### Current Status (July 10, 2025 - DATABASE-FIRST ARCHITECTURE COMPLETE!)
- **Database Models**: 209 total (all with provider prefixes)
- **Providers Table**: 21 providers with complete configuration
  - ✅ All 209 models linked to valid providers with foreign key constraints
  - ✅ ProviderRepository with Redis caching (5-minute TTL)
  - ✅ Direct provider SDK integration (no proxy layer)
- **Working Providers**: 21/21 ✅ **ALL PROVIDERS OPERATIONAL!**
  - ✅ OpenAI (direct SDK integration)
  - ✅ Anthropic (direct SDK with x-api-key authentication)
  - ✅ Google/Gemini (direct Google AI SDK)
  - ✅ Perplexity (direct SDK integration)
  - ✅ Groq (direct SDK integration)
  - ✅ xAI (direct SDK integration)
  - ✅ DeepSeek (direct SDK integration)
  - ✅ Mistral (direct SDK integration)
  - ✅ Cohere (direct SDK integration)
  - ✅ OpenRouter (direct API integration)
  - ✅ Together AI (direct SDK integration)
  - ✅ Plus 10 additional providers in Providers table

#### What We Accomplished Today (July 10)
1. **Providers Table**: Created complete schema with 21 providers and full configuration
2. **Foreign Key Constraints**: All 209 models properly linked to valid providers
3. **Repository Pattern**: Implemented ProviderRepository with Redis caching (5-minute TTL)
4. **Direct SDK Integration**: All providers use their specific SDKs (Google SDK, Anthropic SDK, etc.)
5. **Architecture Migration**: **COMPLETELY REMOVED** all LiteLLM dependencies and code
6. **Database-First Success**: Achieved 100% database-first architecture (21/21 providers)

#### Critical Database Architecture Rules
- **Database Format = AI SDK Format**: Both use `provider/model` (e.g., `openai/gpt-4o`)
- **NO transformation needed**: Database canonicalName maps directly to provider SDKs
- **Consistent prefixes REQUIRED**: 
  - OpenAI: `openai/` (e.g., `openai/gpt-4o`, `openai/dall-e-3`)
  - Anthropic: `anthropic/`
  - Google: `gemini/` (for Google AI SDK)
  - xAI: `xai/` (e.g., `xai/grok-3`, `xai/grok-3-mini`, `xai/grok-2-1212`)
  - Together AI: `together_ai/`
  - OpenRouter: `openrouter/`
  - Alibaba: `alibaba/`
  - All other providers: `provider/`

#### Database-First Provider Configuration
```
Providers Table (21 providers):
openai        ✅ Direct OpenAI SDK integration
anthropic     ✅ Direct Anthropic SDK (x-api-key auth)
google        ✅ Direct Google AI SDK
perplexity    ✅ Direct Perplexity SDK
groq          ✅ Direct Groq SDK
deepseek      ✅ Direct DeepSeek SDK
mistral       ✅ Direct Mistral SDK
cohere        ✅ Direct Cohere SDK
alibaba       ✅ Direct Alibaba API
openrouter    ✅ Direct OpenRouter API
xai           ✅ Direct xAI SDK
together      ✅ Direct Together AI SDK
+ 9 more providers in Providers table
```

### Provider-Specific Configuration

#### xAI Models (FIXED July 2025)
- **Issue**: Stored credential was truncated to 8 characters in LiteLLM
- **Solution**: Re-added all xAI models with proper configuration
- **Status**: All 8 xAI models working (grok-3, grok-3-mini, grok-2-1212 series)
- **API Key**: Stored in .env as `XAI_API_KEY` (full key starting with `xai-`)
- **Note**: Authentication appears to be handled internally by LiteLLM

#### Together AI Models (FIXED July 2025)
- **Issue**: Credential reference system not working
- **Solution**: Re-added models with proper configuration
- **Status**: All 45 Together AI models working
- **API Key**: Stored as `TOGETHER_API_KEY` in .env
- **Models**: 45 models including Llama, QwQ, Mixtral series
- **Note**: Authentication appears to be handled internally by LiteLLM

#### Alibaba/Qwen Models Configuration
- Alibaba/Qwen models are correctly configured - they use `alibaba/` prefix for identification and `openai/` as LiteLLM Model Name because they use the OpenAI-compatible API

### Key LiteLLM Discoveries
1. **Database IDs**: Available in `model_info.id` field (UUID format)
2. **Display Names**: 19 protected aliases that LiteLLM won't let us delete
3. **Model Count**: Database has 210, LiteLLM has 239 (includes display names + OpenRouter extras)
4. **New Models Added**: 44 genuinely new models (25 OpenAI, 7 Perplexity, 12 OpenRouter)
5. **xAI Sync**: Database stores as `grok-3`, LiteLLM uses `xai/grok-3` (both are correct)

### Current Model Status (July 2025)
- ✅ All 210 database models are in LiteLLM
- ✅ All providers working (xAI and Together AI fixed)
- ⚠️  19 display name duplicates remain (LiteLLM protected)
- ✅ Successfully cleaned up test models and most duplicates

### What We Fixed Today
1. **xAI Authentication**: Re-added all models after credential was truncated
2. **Together AI**: Fixed models that had credential reference issues
3. **Duplicates**: Removed 125+ duplicate entries using database IDs
4. **Test Models**: Removed 9 test models (test-env-ref, etc.)
5. **Missing Models**: Added 5 missing Alibaba models

### 🔍 Testing API Keys with Curl (RECOMMENDED)

**ALWAYS test API keys directly with curl before adding to LiteLLM:**

#### Check Available Models
```bash
# OpenAI models
curl -s https://api.openai.com/v1/models -H "Authorization: Bearer $OPENAI_API_KEY" | jq .

# Anthropic models
curl -s https://api.anthropic.com/v1/models -H "x-api-key: $ANTHROPIC_API_KEY" -H "anthropic-version: 2023-06-01" | jq .

# xAI models
curl -s https://api.x.ai/v1/models -H "Authorization: Bearer $XAI_API_KEY" | jq .

# Groq models
curl -s https://api.groq.com/openai/v1/models -H "Authorization: Bearer $GROQ_API_KEY" | jq .
```

#### Anthropic API Testing
```bash
# ✅ CORRECT - Use x-api-key header
curl -s https://api.anthropic.com/v1/messages \
  --header "x-api-key: sk-ant-api03-YOUR-KEY-HERE" \
  --header "anthropic-version: 2023-06-01" \
  --header "content-type: application/json" \
  --data '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 10,
    "messages": [{"role": "user", "content": "Hi"}]
  }'

# ❌ WRONG - Don't use Authorization Bearer for Anthropic
curl -s https://api.anthropic.com/v1/messages \
  --header "Authorization: Bearer sk-ant-api03-YOUR-KEY-HERE"  # This will fail!
```

#### OpenAI API Testing  
```bash
# ✅ OpenAI uses Authorization Bearer
curl -s https://api.openai.com/v1/chat/completions \
  --header "Authorization: Bearer sk-proj-YOUR-KEY-HERE" \
  --header "content-type: application/json" \
  --data '{
    "model": "gpt-4o-mini",
    "max_tokens": 10,
    "messages": [{"role": "user", "content": "Hi"}]
  }'
```

#### Other Providers
- **xAI**: Uses `Authorization: Bearer xai-YOUR-KEY`
- **Google**: Uses `Authorization: Bearer` with API key
- **Groq**: Uses `Authorization: Bearer gsk_YOUR-KEY`
- **Most others**: Use `Authorization: Bearer` header

**Key Point**: Anthropic is unique in using `x-api-key` header instead of `Authorization: Bearer`

### How to Manage LiteLLM Models

#### Critical Provider-Specific Configurations (July 4, 2025)

##### Anthropic Models - WORKING ✅ (Fixed July 4, 2025)
```python
# CORRECT configuration using litellm_credential_name
anthropic_mapping = {
    'anthropic/claude-3.5-sonnet': 'claude-3-5-sonnet-20241022',
    'anthropic/claude-3.5-haiku': 'claude-3-5-haiku-20241022',
    'anthropic/claude-3-opus-latest': 'claude-3-opus-20240229',
    'anthropic/claude-3-haiku-20240307': 'claude-3-haiku-20240307',
    # etc.
}

# 1. First create the credential
credential_payload = {
    'credential_name': 'anthropic-api-key',
    'credential_info': {
        'custom_llm_provider': 'anthropic'
    },
    'credential_values': {
        'api_key': '************************************************************************************************************'
    }
}

# 2. Then add models using the credential
payload = {
    'model_name': 'anthropic/claude-3.5-sonnet',  # Database name
    'litellm_params': {
        'model': 'claude-3-5-sonnet-20241022',    # Anthropic API model name (NO prefix!)
        'litellm_credential_name': 'anthropic-api-key'  # Reference to credential
    }
}

# 3. Test with curl (ALWAYS use x-api-key header!)
curl -s https://api.anthropic.com/v1/messages \
  --header "x-api-key: ************************************************************************************************************" \
  --header "anthropic-version: 2023-06-01" \
  --header "content-type: application/json" \
  --data '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 10,
    "messages": [{"role": "user", "content": "Hi"}]
  }'
```

**CRITICAL ANTHROPIC AUTHENTICATION RULES:**
- ✅ **Use `x-api-key` header** (NOT `Authorization: Bearer`)
- ✅ **Use `litellm_credential_name`** instead of direct API key
- ✅ **API model names have NO prefix** (`claude-3-5-sonnet-20241022`)
- ✅ **Database names keep prefix** (`anthropic/claude-3.5-sonnet`)
- ✅ **ALWAYS test with curl first** using x-api-key header

##### Alibaba Models - NOT WORKING ❌
```python
# Authentication failing - API key is invalid
# Current key: sk-546141d553fa490c9719e806dc4c4a94
# Key format: 'sk-' format IS valid for OpenAI-compatible API (confirmed by Perplexity)
# API endpoint: https://dashscope.aliyuncs.com/compatible-mode/v1

# Tested configurations (all return "Incorrect API key"):
# 1. model: 'openai/qwen-max' with api_base
# 2. model: 'qwen-max' with custom_llm_provider: 'openai'
# 3. Various combinations of prefixes and settings

# ISSUE: The API key itself is invalid/expired/incorrect
# - NOT a format issue (sk- keys are valid for OpenAI-compatible mode)
# - NOT a configuration issue (tried all recommended settings)
# - The key simply doesn't authenticate with Alibaba Cloud

# Action Required: Obtain a VALID API key from Alibaba Cloud Model Studio
# The key can be either format:
# - sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (for OpenAI-compatible API)
# - dashscope.api.xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (for native API)
```

#### Complete Sync Script (July 4, 2025)
```bash
# To sync LiteLLM with database:
python3 /tmp/sync_litellm_final.py

# This script:
# 1. Clears ALL models from LiteLLM
# 2. Adds all 205 database models
# 3. Uses correct credential names
# 4. Handles direct API keys for xAI/Together
# 5. Maps Anthropic model names correctly
```

#### Working Credential Names (Verified July 4, 2025)
```python
credential_map = {
    'openai': 'openai-api-key',          # ✅ Working
    'google': 'google-api-key',          # ✅ Working
    'openrouter': 'OpenRouter',          # ✅ Working
    'groq': 'groq-api-key',              # ✅ Working
    'deepseek': 'deepseek-api-key',      # ✅ Working
    'mistral': 'mistral-api-key',        # ✅ Working
    'perplexity': 'perplexity-api-key',  # ✅ Working
    'cohere': 'cohere-api-key',          # ✅ Working
    'alibaba': 'alibaba-api-key',        # ❌ Auth failing
}

# Direct API keys (credential system not working):
direct_keys = {
    'xai': '************************************************************************************',               # ✅ Working
    'together': '0d49e72a417daa89ef91f8aa0e2833ccacb0630b331e7122b3d8f1cb91709553',          # ✅ Working
    'anthropic': '************************************************************************************************************'     # ✅ Working
}
```

#### Removing Duplicate Models
```python
# Get model info first
response = requests.get(f'{LITELLM_URL}/model/info', headers=headers)
models = response.json().get('data', [])

# Find the database ID
for model in models:
    if model['model_name'] == 'model-to-delete':
        db_id = model['model_info']['id']
        
        # Delete using database ID
        response = requests.post(
            f'{LITELLM_URL}/model/delete',
            headers=headers,
            json={'id': db_id}  # Must use 'id' with the UUID
        )
```

### Common LiteLLM Issues & Solutions

#### xAI "Invalid API key" Error
- **Cause**: Credential was truncated to 8 characters
- **Solution**: Re-add models with full API key from .env
- **Status**: Fixed - all xAI models working

#### Together AI Authentication Failures
- **Cause**: Credential reference system not working
- **Solution**: Use direct API key instead of credential reference
- **Status**: Fixed - all Together AI models working

#### Display Name Models (Shortcuts)
- **What**: 18 models without provider prefix (e.g., `gpt-4o`, `claude-3-opus-20240229`)
- **Reason**: Protected by foreign key constraints - system aliases for compatibility
- **Action**: Cannot be deleted - these are built-in LiteLLM shortcuts

#### Alibaba Authentication Error
- **Cause**: Invalid/expired API key (returns "Incorrect API key provided")
- **Current Key**: `sk-546141d553fa490c9719e806dc4c4a94`
- **Key Format**: `sk-` format IS valid for OpenAI-compatible API
- **Solution**: Need a VALID/ACTIVE API key from Alibaba Cloud Model Studio

#### Model Count Summary
- **Database**: 205 models (all with provider prefixes)
- **LiteLLM**: 199 models total
  - 181 provider-prefixed models (179 working + 2 Alibaba not working)
  - 18 display name shortcuts (system aliases)

... [rest of the existing content remains the same]

## 🚀 Deployment Process

For complete deployment instructions using the v3.2 scripts, see:
**`~/deployments/CLAUDE.md`** - Section "🚀 Deployment Process"

### Quick Reference
- **Staging**: `./deploy-staging-v3.sh` (from develop branch)
- **Production**: `./deploy-production-v3.sh` (from main branch)
- **Timeout**: Always set `export BASH_DEFAULT_TIMEOUT_MS=1200000` first

### Development-Specific Notes
- Model statistics are now generated at runtime (no build-time generation)
- Each environment has separate `.env` and `.env.local` files
- Redis must use `localhost` not `127.0.0.1` in URLs
- All deployment logs saved to `/home/<USER>/deployments/logs/deploy-*.log`

---
**Environment Implementation**: All environments, configuration, and deployment procedures
**Last Updated**: July 6, 2025